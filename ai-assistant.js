/**
 * 数据安全小卫士AI助手
 * 使用GLM4.5 API提供学习指导
 */

class AIAssistant {
    constructor(config = {}) {
        this.config = {
            // GLM4.5 API配置
            apiKey: config.apiKey || '31c24cd84f6141e2baf6ea33df124055.BBvmeBEzCdBmzdTf', // 需要用户配置
            apiUrl: config.apiUrl || 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
            model: config.model || 'GLM-4.5',
            
            // 助手配置
            name: config.name || '小卫士',
            avatar: config.avatar || '🛡️',
            position: config.position || 'bottom-right',
            
            // 学习内容配置
            subject: config.subject || '数据安全',
            context: config.context || '',
            
            // 界面配置
            theme: config.theme || 'blue'
        };
        
        this.isOpen = true; // 默认打开状态
        this.isMinimized = false;
        this.conversationHistory = [];
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.createAssistantUI();
        this.bindEvents();
        
        // 自动打开AI助手窗口
        setTimeout(() => {
            this.openModernChat();
        }, 100);
    }
    
    // 获取头像HTML - 支持图片和emoji
    getAvatarHTML(avatar) {
        if (avatar && (avatar.endsWith('.png') || avatar.endsWith('.jpg') || avatar.endsWith('.jpeg') || avatar.endsWith('.gif') || avatar.endsWith('.webp'))) {
            return `<img src="${avatar}" alt="AI助手头像" onerror="this.style.display='none'; this.parentNode.innerHTML='🤖';">`;
        } else {
            return avatar || '🤖';
        }
    }
    
         createAssistantUI() {
         // 创建助手容器
         const assistantContainer = document.createElement('div');
         assistantContainer.id = 'ai-assistant';
         assistantContainer.className = `ai-assistant ${this.config.position}`;
         
         assistantContainer.innerHTML = `
            <!-- 助手头像按钮 -->
            <div class="assistant-avatar" title="点击我询问学习问题">
                <div class="avatar-icon">${this.getAvatarHTML(this.config.avatar)}</div>
                <div class="pulse-ring"></div>
                <div class="notification-dot" style="display: none;"></div>
                <div class="message-count" style="display: none;">1</div>
            </div>
             
             <!-- 现代化对话窗口 -->
             <div class="modern-chat-window" style="display: none;">
                 <!-- 顶部栏 -->
                 <div class="modern-header">
                    <div class="header-left">
                        <div class="assistant-avatar-mini">${this.getAvatarHTML(this.config.avatar)}</div>
                        <div class="assistant-info">
                            <div class="assistant-name">${this.config.name}</div>
                            <div class="assistant-status">
                                <div class="status-dot"></div>
                                <span>在线助手</span>
                            </div>
                        </div>
                    </div>
                     <div class="header-actions">
                         <button class="minimize-btn" title="最小化">−</button>
                         <button class="close-btn" title="关闭">×</button>
                     </div>
                 </div>
                 
                 <!-- 消息区域 -->
                 <div class="modern-messages-container" id="modern-messages">
                    <!-- 欢迎消息 -->
                    <div class="welcome-message">
                        <div class="welcome-avatar">${this.getAvatarHTML(this.config.avatar)}</div>
                        <div class="welcome-content">
                            <div class="welcome-title">你好！我是${this.config.name}</div>
                            <div class="welcome-subtitle">你的数据安全学习助手，有什么关于<strong>${this.config.context}</strong>的问题想问我吗？</div>
                        </div>
                    </div>
                     
                     <!-- 快速问题卡片 -->
                     <div class="quick-questions-card" id="quick-questions-card">
                         <div class="card-header">
                             <div class="card-icon">💡</div>
                             <div class="card-title">常见问题</div>
                         </div>
                         <div class="quick-questions-grid" id="quick-questions-grid"></div>
                     </div>
                 </div>
                 
                 <!-- 输入区域 -->
                 <div class="modern-input-area">
                     <div class="input-container">
                         <div class="input-wrapper">
                             <input type="text" class="modern-input" placeholder="输入你的问题..." maxlength="200">
                             <button class="modern-send-btn" disabled>
                                 <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                     <line x1="22" y1="2" x2="11" y2="13"></line>
                                     <polygon points="22,2 15,22 11,13 2,9"></polygon>
                                 </svg>
                             </button>
                         </div>
                     </div>
                 </div>
                 
                 <!-- 滚动到底部按钮 -->
                 <div class="scroll-to-bottom-modern" id="scroll-to-bottom-modern" style="display: none;">
                     <button class="scroll-btn-modern">
                         <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                             <polyline points="6,9 12,15 18,9"></polyline>
                         </svg>
                         新消息
                     </button>
                 </div>
             </div>
         `;
        
        document.body.appendChild(assistantContainer);
        this.assistantElement = assistantContainer;
        
        // 添加样式
        this.addStyles();
        
        // 延迟生成快速问题，确保DOM完全渲染
        setTimeout(() => {
            this.generateQuickQuestions();
        }, 100);
    }
    
         addStyles() {
         const style = document.createElement('style');
         style.textContent = `
             .ai-assistant {
                 position: fixed;
                 z-index: 10000;
                 font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                 pointer-events: auto;
                 user-select: none;
             }
             
             /* 确保AI助手始终可见 */
             .ai-assistant * {
                 pointer-events: auto;
             }
             
             .ai-assistant.bottom-right {
                 top: 50%;
                 transform: translateY(-50%);
                 right: 20px;
             }
             
             .ai-assistant.bottom-left {
                 top: 50%;
                 transform: translateY(-50%);
                 left: 20px;
             }
             
             .ai-assistant.top-right {
                 top: 50%;
                 transform: translateY(-50%);
                 right: 20px;
             }
            
            .assistant-avatar {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                position: relative;
                transition: all 0.3s ease;
                animation: float 3s ease-in-out infinite;
            }
            
            .assistant-avatar:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
            }
            
            .avatar-icon {
                font-size: 24px;
                color: white;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                overflow: hidden;
            }
            
            .avatar-icon img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 50%;
            }
            
            .pulse-ring {
                position: absolute;
                width: 100%;
                height: 100%;
                border: 2px solid rgba(102, 126, 234, 0.6);
                border-radius: 50%;
                animation: pulse 2s ease-in-out infinite;
            }
            
                         .notification-dot {
                 position: absolute;
                 top: 5px;
                 right: 5px;
                 width: 12px;
                 height: 12px;
                 background: #ff4757;
                 border-radius: 50%;
                 border: 2px solid white;
                 animation: bounce 1s infinite;
             }
             
             .message-count {
                 position: absolute;
                 top: -5px;
                 right: -5px;
                 background: linear-gradient(135deg, #ff4757, #ff3838);
                 color: white;
                 border-radius: 50%;
                 min-width: 20px;
                 height: 20px;
                 font-size: 11px;
                 font-weight: 600;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 border: 2px solid white;
                 box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
                 animation: messageCountPulse 2s infinite;
             }
            
            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-5px); }
            }
            
            @keyframes pulse {
                0% { transform: scale(1); opacity: 1; }
                100% { transform: scale(1.3); opacity: 0; }
            }
            
                         @keyframes bounce {
                 0%, 100% { transform: scale(1); }
                 50% { transform: scale(1.2); }
             }
             
             @keyframes messageCountPulse {
                 0%, 100% { transform: scale(1); }
                 50% { transform: scale(1.1); }
             }
             
             /* 现代化对话窗口样式 */
             .modern-chat-window {
                 position: absolute;
                 top: 50%;
                 transform: translateY(-50%);
                 width: 380px;
                 height: 600px;
                 background: rgba(255, 255, 255, 0.15);
                 backdrop-filter: blur(20px);
                 -webkit-backdrop-filter: blur(20px);
                 border: 1px solid rgba(255, 255, 255, 0.2);
                 border-radius: 20px;
                 box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
                 display: flex;
                 flex-direction: column;
                 overflow: hidden;
                 transform: translateY(-50%) scale(0.95);
                 opacity: 0;
                 transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
             }
             
             .ai-assistant.bottom-right .modern-chat-window {
                 right: 75px;
             }
             
             .ai-assistant.bottom-left .modern-chat-window {
                 left: 75px;
             }
             
             .modern-chat-window.show {
                 transform: translateY(-50%) scale(1);
                 opacity: 1;
             }
             
             /* 现代化头部样式 */
             .modern-header {
                 background: rgba(102, 126, 234, 0.2);
                 backdrop-filter: blur(15px);
                 -webkit-backdrop-filter: blur(15px);
                 border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                 color: #333;
                 padding: 16px 20px;
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 border-radius: 20px 20px 0 0;
                 position: relative;
             }
             
             .header-left {
                 display: flex;
                 align-items: center;
                 gap: 12px;
             }
             
            .assistant-avatar-mini {
                width: 36px;
                height: 36px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                overflow: hidden;
            }
            
            .assistant-avatar-mini img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 12px;
            }
             
             .assistant-info {
                 display: flex;
                 flex-direction: column;
                 gap: 2px;
             }
             
             .assistant-name {
                 font-weight: 600;
                 font-size: 16px;
                 line-height: 1.2;
             }
             
             .assistant-status {
                 display: flex;
                 align-items: center;
                 gap: 6px;
                 font-size: 12px;
                 opacity: 0.9;
             }
             
             .status-dot {
                 width: 6px;
                 height: 6px;
                 background: #4ade80;
                 border-radius: 50%;
                 animation: statusPulse 2s infinite;
             }
             
             @keyframes statusPulse {
                 0%, 100% { opacity: 1; }
                 50% { opacity: 0.5; }
             }
             
             .header-actions {
                 display: flex;
                 gap: 8px;
             }
             
             .minimize-btn, .close-btn {
                 background: rgba(255, 255, 255, 0.15);
                 border: none;
                 color: white;
                 width: 32px;
                 height: 32px;
                 border-radius: 8px;
                 cursor: pointer;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 transition: all 0.2s ease;
                 backdrop-filter: blur(10px);
                 font-size: 16px;
                 font-weight: 500;
             }
             
             .minimize-btn:hover, .close-btn:hover {
                 background: rgba(255, 255, 255, 0.25);
                 transform: scale(1.05);
             }
             
             /* 现代化消息区域 */
             .modern-messages-container {
                 flex: 1;
                 padding: 20px;
                 overflow-y: auto;
                 background: rgba(255, 255, 255, 0.05);
                 backdrop-filter: blur(10px);
                 -webkit-backdrop-filter: blur(10px);
                 scroll-behavior: smooth;
                 position: relative;
             }
             
             .modern-messages-container::-webkit-scrollbar {
                 width: 4px;
             }
             
             .modern-messages-container::-webkit-scrollbar-track {
                 background: rgba(0, 0, 0, 0.05);
                 border-radius: 2px;
             }
             
             .modern-messages-container::-webkit-scrollbar-thumb {
                 background: rgba(102, 126, 234, 0.3);
                 border-radius: 2px;
             }
             
             .modern-messages-container::-webkit-scrollbar-thumb:hover {
                 background: rgba(102, 126, 234, 0.5);
             }
             
             /* 欢迎消息样式 */
             .welcome-message {
                 background: white;
                 border-radius: 16px;
                 padding: 20px;
                 margin-bottom: 16px;
                 box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
                 border: 1px solid rgba(0, 0, 0, 0.05);
                 display: flex;
                 align-items: flex-start;
                 gap: 16px;
                 animation: slideInUp 0.6s ease-out;
             }
             
            .welcome-avatar {
                width: 48px;
                height: 48px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 14px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                color: white;
                flex-shrink: 0;
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                overflow: hidden;
            }
            
            .welcome-avatar img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 14px;
            }
             
             .welcome-content {
                 flex: 1;
             }
             
             .welcome-title {
                 font-size: 16px;
                 font-weight: 600;
                 color: #1f2937;
                 margin-bottom: 8px;
                 line-height: 1.3;
             }
             
             .welcome-subtitle {
                 font-size: 14px;
                 color: #6b7280;
                 line-height: 1.5;
             }
             
             .welcome-subtitle strong {
                 color: #4f46e5;
                 font-weight: 600;
             }
             
             /* 快速问题卡片 */
             .quick-questions-card {
                 background: rgba(255, 255, 255, 0.15);
                 backdrop-filter: blur(15px);
                 -webkit-backdrop-filter: blur(15px);
                 border: 1px solid rgba(255, 255, 255, 0.2);
                 border-radius: 16px;
                 padding: 16px;
                 box-shadow: 0 4px 16px rgba(31, 38, 135, 0.3);
                 animation: slideInUp 0.6s ease-out 0.2s both;
             }
             
             .card-header {
                 display: flex;
                 align-items: center;
                 gap: 8px;
                 margin-bottom: 12px;
             }
             
             .card-icon {
                 font-size: 16px;
             }
             
             .card-title {
                 font-weight: 600;
                 color: #1f2937;
                 font-size: 14px;
             }
             
             .quick-questions-grid {
                 display: grid;
                 gap: 8px;
                 grid-template-columns: 1fr;
             }
             
             .quick-question-item {
                 background: #f8fafc;
                 border: 1px solid #e2e8f0;
                 border-radius: 12px;
                 padding: 12px 16px;
                 cursor: pointer;
                 transition: all 0.3s ease;
                 font-size: 13px;
                 color: #475569;
                 text-align: left;
                 font-weight: 500;
                 position: relative;
                 overflow: hidden;
             }
             
             .quick-question-item::before {
                 content: '';
                 position: absolute;
                 top: 0;
                 left: -100%;
                 width: 100%;
                 height: 100%;
                 background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
                 transition: left 0.5s ease;
             }
             
             .quick-question-item:hover {
                 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                 color: white;
                 transform: translateY(-2px);
                 box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
                 border-color: transparent;
             }
             
             .quick-question-item:hover::before {
                 left: 100%;
             }
             
             /* 对话消息样式 */
             .chat-message {
                 margin-bottom: 16px;
                 display: flex;
                 align-items: flex-end;
                 gap: 12px;
                 animation: slideInUp 0.4s ease-out;
             }
             
             .chat-message.user-message {
                 flex-direction: row-reverse;
             }
             
            .message-avatar-modern {
                width: 32px;
                height: 32px;
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: white;
                flex-shrink: 0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                overflow: hidden;
            }
            
            .message-avatar-modern img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 12px;
            }
             
             .chat-message.assistant-message .message-avatar-modern {
                 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
             }
             
             .chat-message.user-message .message-avatar-modern {
                 background: linear-gradient(135deg, #10b981 0%, #059669 100%);
             }
             
             .message-bubble {
                 background: white;
                 border-radius: 18px;
                 padding: 14px 18px;
                 max-width: 280px;
                 box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
                 border: 1px solid rgba(0, 0, 0, 0.05);
                 word-wrap: break-word;
                 word-break: break-word;
                 overflow-wrap: break-word;
                 position: relative;
                 line-height: 1.5;
             }
             
             .chat-message.user-message .message-bubble {
                 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                 color: white;
                 border: none;
                 box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
             }
             
             .message-bubble p {
                 margin: 0;
                 font-size: 14px;
                 line-height: 1.5;
             }
             
             .message-bubble p:not(:last-child) {
                 margin-bottom: 8px;
             }
             
             /* 加载消息样式 */
             .loading-message-modern {
                 background: white;
                 border-radius: 18px;
                 padding: 14px 18px;
                 display: flex;
                 align-items: center;
                 gap: 12px;
                 box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
                 border: 1px solid rgba(0, 0, 0, 0.05);
                 max-width: 120px;
             }
             
             .loading-dots-modern {
                 display: flex;
                 gap: 4px;
             }
             
             .loading-dot-modern {
                 width: 6px;
                 height: 6px;
                 border-radius: 50%;
                 background: #667eea;
                 animation: loadingDotBounce 1.4s ease-in-out infinite both;
             }
             
             .loading-dot-modern:nth-child(1) { animation-delay: -0.32s; }
             .loading-dot-modern:nth-child(2) { animation-delay: -0.16s; }
             .loading-dot-modern:nth-child(3) { animation-delay: 0s; }
             
             @keyframes loadingDotBounce {
                 0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
                 40% { transform: scale(1.2); opacity: 1; }
             }
             
             /* 现代化输入区域 */
             .modern-input-area {
                 padding: 16px 20px;
                 background: rgba(255, 255, 255, 0.1);
                 backdrop-filter: blur(10px);
                 -webkit-backdrop-filter: blur(10px);
                 border-top: 1px solid rgba(255, 255, 255, 0.2);
                 border-radius: 0 0 20px 20px;
             }
             
             .input-container {
                 position: relative;
             }
             
             .input-wrapper {
                 display: flex;
                 align-items: center;
                 background: #f9fafb;
                 border: 2px solid #e5e7eb;
                 border-radius: 24px;
                 padding: 4px;
                 transition: all 0.3s ease;
                 position: relative;
                 overflow: hidden;
             }
             
             .input-wrapper::before {
                 content: '';
                 position: absolute;
                 top: 0;
                 left: -100%;
                 width: 100%;
                 height: 100%;
                 background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
                 transition: left 0.6s ease;
             }
             
             .input-wrapper:focus-within {
                 border-color: #667eea;
                 box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                 background: white;
             }
             
             .input-wrapper:focus-within::before {
                 left: 100%;
             }
             
             .modern-input {
                 flex: 1;
                 border: none;
                 background: transparent;
                 padding: 12px 16px;
                 font-size: 14px;
                 outline: none;
                 color: #1f2937;
                 font-weight: 500;
             }
             
             .modern-input::placeholder {
                 color: #9ca3af;
                 font-weight: 400;
             }
             
             .modern-send-btn {
                 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                 border: none;
                 color: white;
                 width: 40px;
                 height: 40px;
                 border-radius: 50%;
                 cursor: pointer;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 transition: all 0.3s ease;
                 margin-right: 2px;
                 box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
             }
             
             .modern-send-btn:hover:not(:disabled) {
                 background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                 transform: scale(1.05);
                 box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
             }
             
             .modern-send-btn:disabled {
                 background: #d1d5db;
                 cursor: not-allowed;
                 transform: none;
                 box-shadow: none;
             }
             
             /* 滚动到底部按钮 */
             .scroll-to-bottom-modern {
                 position: absolute;
                 bottom: 90px;
                 right: 20px;
                 z-index: 10;
                 opacity: 0;
                 transform: translateY(10px);
                 transition: all 0.3s ease;
                 pointer-events: none;
             }
             
             .scroll-to-bottom-modern.show {
                 opacity: 1;
                 transform: translateY(0);
                 pointer-events: auto;
             }
             
             .scroll-btn-modern {
                 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                 color: white;
                 border: none;
                 border-radius: 24px;
                 padding: 12px 16px;
                 font-size: 12px;
                 font-weight: 600;
                 cursor: pointer;
                 box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
                 transition: all 0.3s ease;
                 display: flex;
                 align-items: center;
                 gap: 6px;
                 backdrop-filter: blur(10px);
             }
             
             .scroll-btn-modern:hover {
                 transform: translateY(-2px);
                 box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
             }
             
             /* 动画效果 */
             @keyframes slideInUp {
                 from {
                     opacity: 0;
                     transform: translateY(20px);
                 }
                 to {
                     opacity: 1;
                     transform: translateY(0);
                 }
             }
             
             @keyframes slideOutUp {
                 from {
                     opacity: 1;
                     transform: translateY(0);
                 }
                 to {
                     opacity: 0;
                     transform: translateY(-20px);
                 }
             }
            
            .chat-window {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 350px;
                height: 500px;
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
                display: flex;
                flex-direction: column;
                overflow: hidden;
                transform: translateY(-50%) scale(0.8);
                opacity: 0;
                transition: all 0.3s ease;
                z-index: 1000;
            }
            
            .ai-assistant.bottom-right .chat-window {
                right: 70px;
            }
            
            .ai-assistant.bottom-left .chat-window {
                left: 70px;
            }
            
            .chat-window.show {
                transform: translateY(-50%) scale(1);
                opacity: 1;
            }
            
            .chat-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .assistant-info {
                display: flex;
                flex-direction: column;
            }
            
            .assistant-name {
                font-weight: bold;
                font-size: 16px;
            }
            
            .assistant-status {
                font-size: 12px;
                opacity: 0.8;
            }
            
            .close-btn {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s;
            }
            
            .close-btn:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
            
            .chat-messages {
                flex: 1;
                padding: 15px;
                overflow-y: auto;
                background: #f8f9fa;
                scroll-behavior: smooth;
                min-height: 0; /* 允许flex项目收缩 */
            }
            
            .message {
                margin-bottom: 15px;
                display: flex;
                align-items: flex-start;
                gap: 10px;
                animation: messageSlideIn 0.3s ease;
            }
            
            @keyframes messageSlideIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            .user-message {
                flex-direction: row-reverse;
            }
            
            .message-avatar {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: white;
                flex-shrink: 0;
            }
            
            .user-message .message-avatar {
                background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            }
            
            .message-content {
                background: white;
                padding: 10px 15px;
                border-radius: 15px;
                max-width: 250px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                line-height: 1.4;
            }
            
            .user-message .message-content {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
            
            .message-content p {
                margin: 0 0 8px 0;
            }
            
            .message-content p:last-child {
                margin-bottom: 0;
            }
            
            .loading-message {
                display: flex;
                align-items: center;
                gap: 8px;
                color: #666;
                font-style: italic;
            }
            
            .loading-dots {
                display: flex;
                gap: 2px;
            }
            
            .loading-dot {
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background: #667eea;
                animation: loadingDot 1.4s ease-in-out infinite both;
            }
            
            .loading-dot:nth-child(1) { animation-delay: -0.32s; }
            .loading-dot:nth-child(2) { animation-delay: -0.16s; }
            .loading-dot:nth-child(3) { animation-delay: 0s; }
            
            @keyframes loadingDot {
                0%, 80%, 100% { transform: scale(0); }
                40% { transform: scale(1); }
            }
            
            .chat-input-container {
                padding: 15px;
                border-top: 1px solid #eee;
                display: flex;
                gap: 10px;
                background: white;
                flex-shrink: 0;
                min-height: 60px;
                box-sizing: border-box;
            }
            
            .chat-input {
                flex: 1;
                border: 1px solid #ddd;
                border-radius: 20px;
                padding: 10px 15px;
                outline: none;
                font-size: 14px;
                transition: border-color 0.2s;
            }
            
            .chat-input:focus {
                border-color: #667eea;
            }
            
            .send-btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 20px;
                padding: 10px 20px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.2s;
            }
            
            .send-btn:hover:not(:disabled) {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }
            
            .send-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }
            
            .quick-questions {
                padding: 15px;
                border-top: 1px solid #eee;
                background: #f8f9fa;
                flex-shrink: 0;
                max-height: 120px;
                overflow-y: auto;
            }
            
            .quick-question-title {
                font-size: 12px;
                color: #666;
                margin-bottom: 8px;
                font-weight: 500;
            }
            
            .quick-question-buttons {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
            }
            
            .quick-question-btn {
                background: white;
                border: 1px solid #ddd;
                border-radius: 15px;
                padding: 6px 12px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s;
                color: #666;
            }
            
            .quick-question-btn:hover {
                background: #667eea;
                color: white;
                border-color: #667eea;
                transform: translateY(-1px);
            }
            
            .scroll-to-bottom {
                position: absolute;
                bottom: 180px;
                right: 15px;
                z-index: 10;
                opacity: 0;
                transform: translateY(10px);
                transition: all 0.3s ease;
                pointer-events: none;
            }
            
            .scroll-to-bottom.show {
                opacity: 1;
                transform: translateY(0);
                pointer-events: auto;
            }
            
            .scroll-btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 20px;
                padding: 8px 15px;
                font-size: 12px;
                cursor: pointer;
                box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
                transition: all 0.2s ease;
                white-space: nowrap;
            }
            
            .scroll-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
            
            .scroll-btn:active {
                transform: translateY(0);
            }
            
                         /* 响应式设计 */
             @media (max-width: 480px) {
                 .ai-assistant.bottom-right,
                 .ai-assistant.bottom-left,
                 .ai-assistant.top-right {
                     top: 50%;
                     transform: translateY(-50%);
                     right: 10px;
                     left: auto;
                 }
                 
                 .modern-chat-window {
                     width: calc(100vw - 40px);
                     max-width: 340px;
                     height: 500px;
                     transform: translateY(-50%) scale(0.9);
                 }
                 
                 .ai-assistant.bottom-right .modern-chat-window {
                     right: 60px;
                 }
                 
                 .ai-assistant.bottom-left .modern-chat-window {
                     left: 60px;
                 }
                 
                 .modern-chat-window.show {
                     transform: translateY(-50%) scale(1);
                 }
                 
                 .assistant-avatar {
                     width: 50px;
                     height: 50px;
                 }
                 
                 .avatar-icon {
                     font-size: 20px;
                 }
                 
                 .modern-messages-container {
                     padding: 16px;
                 }
                 
                 .welcome-message {
                     padding: 16px;
                     gap: 12px;
                 }
                 
                 .welcome-avatar {
                     width: 40px;
                     height: 40px;
                     font-size: 16px;
                 }
                 
                 .message-bubble {
                     max-width: 240px;
                     padding: 12px 16px;
                 }
                 
                 .modern-input-area {
                     padding: 12px 16px;
                 }
                 
                 .scroll-to-bottom-modern {
                     bottom: 80px;
                     right: 16px;
                 }
             }
        `;
        
        document.head.appendChild(style);
    }
    
         bindEvents() {
         const avatar = this.assistantElement.querySelector('.assistant-avatar');
         const modernChatWindow = this.assistantElement.querySelector('.modern-chat-window');
         const closeBtn = this.assistantElement.querySelector('.close-btn');
         const minimizeBtn = this.assistantElement.querySelector('.minimize-btn');
         const modernInput = this.assistantElement.querySelector('.modern-input');
         const modernSendBtn = this.assistantElement.querySelector('.modern-send-btn');
         
         // 点击头像只能打开对话窗口，不能关闭（需要用关闭按钮）
         avatar.addEventListener('click', () => {
             if (!this.isOpen || this.isMinimized) {
                 this.openModernChat();
             }
         });
         
         // 关闭按钮
         closeBtn.addEventListener('click', () => {
             this.closeModernChat();
         });
         
         // 最小化按钮
         minimizeBtn.addEventListener('click', () => {
             this.minimizeChat();
         });
         
         // 发送消息
         modernSendBtn.addEventListener('click', () => {
             this.sendModernMessage();
         });
         
         // 回车发送
         modernInput.addEventListener('keypress', (e) => {
             if (e.key === 'Enter' && !e.shiftKey) {
                 e.preventDefault();
                 this.sendModernMessage();
             }
         });
         
         // 监听输入变化，控制发送按钮状态
         modernInput.addEventListener('input', () => {
             const hasText = modernInput.value.trim().length > 0;
             modernSendBtn.disabled = !hasText;
         });
         
         // 移除点击外部关闭功能 - AI助手应该持久显示
         // document.addEventListener('click', (e) => {
         //     if (!this.assistantElement.contains(e.target) && this.isOpen) {
         //         this.closeModernChat();
         //     }
         // });
         
         // 滚动到底部按钮
         const scrollToBottomBtn = this.assistantElement.querySelector('#scroll-to-bottom-modern .scroll-btn-modern');
         const messagesContainer = this.assistantElement.querySelector('#modern-messages');
         
         if (scrollToBottomBtn && messagesContainer) {
             // 监听滚动事件
             messagesContainer.addEventListener('scroll', () => {
                 const scrollToBottomDiv = this.assistantElement.querySelector('#scroll-to-bottom-modern');
                 const isAtBottom = messagesContainer.scrollTop >= messagesContainer.scrollHeight - messagesContainer.clientHeight - 50;
                 
                 if (isAtBottom) {
                     scrollToBottomDiv.classList.remove('show');
                 } else {
                     scrollToBottomDiv.classList.add('show');
                 }
             });
             
             // 点击滚动到底部
             scrollToBottomBtn.addEventListener('click', () => {
                 messagesContainer.scrollTo({
                     top: messagesContainer.scrollHeight,
                     behavior: 'smooth'
                 });
             });
         }
     }
    
        generateQuickQuestions() {
        const quickQuestionsContainer = this.assistantElement.querySelector('#quick-questions-grid');
        
        if (!quickQuestionsContainer) return;
        
        // 清空现有的快捷问题
        quickQuestionsContainer.innerHTML = '';
        
        // 强制使用getQuickQuestions()以确保页面匹配正确
        let questions = this.getQuickQuestions();
        
        questions.forEach(question => {
            const btn = document.createElement('button');
            btn.className = 'quick-question-item';
            btn.textContent = question;
            btn.addEventListener('click', () => {
                this.askModernQuestion(question);
            });
            quickQuestionsContainer.appendChild(btn);
        });
    }
    
    // 刷新快捷问题（当页面变化时调用）
    refreshQuickQuestions() {
        this.generateQuickQuestions();
    }
    
   getQuickQuestions() {
       // 根据页面内容返回不同的快速问题
       const currentHref = window.location.href;
       const currentTitle = document.title;
       
       console.log('获取快捷问题 - URL:', currentHref);
       console.log('获取快捷问题 - 标题:', currentTitle);
       
       if (currentHref.includes('数据隐患辨一辨') || currentTitle.includes('数据隐患辨一辨')) {
           console.log('匹配到：数据隐患辨一辨');
           return [
               '什么是数据隐患？',
               '如何识别安全行为？',
               '陌生人加好友安全吗？',
               '为什么不能随便扫码？'
           ];
       } else if (currentHref.includes('数据分享试一试') || currentTitle.includes('数据分享试一试')) {
           console.log('匹配到：数据分享试一试');
           return [
               '朋友圈分享要注意什么？',
               '哪些信息不能随便发？',
               '如何设置隐私权限？',
               '为什么不能发位置信息？'
           ];
       } else if (currentHref.includes('数据备份存一存') || currentTitle.includes('数据备份存一存')) {
           console.log('匹配到：数据备份存一存');
           return [
               '为什么要备份数据？',
               '哪些文件需要备份？',
               '如何选择备份方式？',
               '临时文件需要备份吗？'
           ];
       } else if (currentHref.includes('保护措施选一选') || currentTitle.includes('保护措施选一选')) {
           console.log('匹配到：保护措施选一选');
           return [
               '数据收集应该匹配哪些保护措施？',
               '数据传输为什么要用自己的流量？',
               '数据存储需要哪些保护方法？',
               '数据共享时"谁可以看"是什么意思？',
               '如何正确拖拽匹配？'
           ];
       } else if (currentHref.includes('AI智能评价系统') || currentTitle.includes('AI智能评价系统')) {
           console.log('匹配到：AI智能评价系统');
           return [
               '如何开始AI评价对话？',
               'AI是怎么评价我的学习的？',
               '学习报告包含哪些内容？',
               '如何下载我的学习报告？'
           ];
       } else {
           console.log('匹配到：默认页面');
           return [
               '数据安全为什么重要？',
               '如何开始学习？',
               '有哪些闯关内容？'
           ];
       }
   }
    
         toggleModernChat() {
         if (this.isOpen) {
             this.closeModernChat();
         } else {
             this.openModernChat();
         }
     }
     
     openModernChat() {
         const modernChatWindow = this.assistantElement.querySelector('.modern-chat-window');
         const messagesContainer = this.assistantElement.querySelector('#modern-messages');
         
         modernChatWindow.style.display = 'flex';
         setTimeout(() => {
             modernChatWindow.classList.add('show');
             // 打开窗口时滚动到底部
             requestAnimationFrame(() => {
                 if (messagesContainer) {
                     messagesContainer.scrollTop = messagesContainer.scrollHeight;
                 }
             });
         }, 10);
         this.isOpen = true;
         this.isMinimized = false;
         
         // 隐藏通知点和消息计数
         const notificationDot = this.assistantElement.querySelector('.notification-dot');
         const messageCount = this.assistantElement.querySelector('.message-count');
         if (notificationDot) notificationDot.style.display = 'none';
         if (messageCount) messageCount.style.display = 'none';
     }
     
     closeModernChat() {
         const modernChatWindow = this.assistantElement.querySelector('.modern-chat-window');
         modernChatWindow.classList.remove('show');
         setTimeout(() => {
             modernChatWindow.style.display = 'none';
         }, 400);
         this.isOpen = false;
         this.isMinimized = false;
     }
     
     minimizeChat() {
         this.closeModernChat();
         this.isMinimized = true;
         // 显示消息计数表示有未读消息
         const messageCount = this.assistantElement.querySelector('.message-count');
         if (messageCount) {
             messageCount.style.display = 'flex';
         }
     }
     
     // 兼容旧方法
     toggleChat() { this.toggleModernChat(); }
     openChat() { this.openModernChat(); }
     closeChat() { this.closeModernChat(); }
    
         async sendModernMessage() {
         const modernInput = this.assistantElement.querySelector('.modern-input');
         const message = modernInput.value.trim();
         
         if (!message || this.isLoading) return;
         
         // 添加用户消息
         this.addModernMessage(message, 'user');
         modernInput.value = '';
         
         // 重置发送按钮状态
         const modernSendBtn = this.assistantElement.querySelector('.modern-send-btn');
         modernSendBtn.disabled = true;
         
         // 发送到AI并获取回复
         await this.getAIResponse(message);
     }
     
     async askModernQuestion(question) {
         // 添加用户消息
         this.addModernMessage(question, 'user');
         
         // 发送到AI并获取回复
         await this.getAIResponse(question);
     }
     
     // 兼容旧方法
     async sendMessage() { await this.sendModernMessage(); }
     async askQuestion(question) { await this.askModernQuestion(question); }
    
         addModernMessage(content, type = 'assistant') {
         const messagesContainer = this.assistantElement.querySelector('#modern-messages');
         
         // 隐藏欢迎消息和快速问题（第一次发送消息后）
         if (this.conversationHistory.length === 0) {
             const welcomeMessage = messagesContainer.querySelector('.welcome-message');
             const quickQuestionsCard = messagesContainer.querySelector('.quick-questions-card');
             if (welcomeMessage) {
                 welcomeMessage.style.animation = 'slideOutUp 0.3s ease-out forwards';
                 setTimeout(() => welcomeMessage.style.display = 'none', 300);
             }
             if (quickQuestionsCard) {
                 quickQuestionsCard.style.animation = 'slideOutUp 0.3s ease-out 0.1s forwards';
                 setTimeout(() => quickQuestionsCard.style.display = 'none', 400);
             }
         }
         
         const messageDiv = document.createElement('div');
         messageDiv.className = `chat-message ${type}-message`;
         
        const avatar = type === 'user' ? '👤' : this.getAvatarHTML(this.config.avatar);
        
        messageDiv.innerHTML = `
            <div class="message-avatar-modern">${avatar}</div>
            <div class="message-bubble">
                <p>${content}</p>
            </div>
        `;
         
         messagesContainer.appendChild(messageDiv);
         
         // 自动滚动到底部
         requestAnimationFrame(() => {
             messagesContainer.scrollTop = messagesContainer.scrollHeight;
         });
         
         // 保存到对话历史
         this.conversationHistory.push({
             role: type === 'user' ? 'user' : 'assistant',
             content: content
         });
     }
     
     // 兼容旧方法
     addMessage(content, type = 'assistant') {
         this.addModernMessage(content, type);
     }
    
         showModernLoadingMessage() {
         const messagesContainer = this.assistantElement.querySelector('#modern-messages');
         
         const loadingDiv = document.createElement('div');
         loadingDiv.className = 'chat-message assistant-message';
         loadingDiv.id = 'modern-loading-message';
         
        loadingDiv.innerHTML = `
            <div class="message-avatar-modern">${this.getAvatarHTML(this.config.avatar)}</div>
            <div class="loading-message-modern">
                <span style="font-size: 13px; color: #6b7280;">正在思考中</span>
                <div class="loading-dots-modern">
                    <div class="loading-dot-modern"></div>
                    <div class="loading-dot-modern"></div>
                    <div class="loading-dot-modern"></div>
                </div>
            </div>
        `;
         
         messagesContainer.appendChild(loadingDiv);
         
         // 确保滚动到底部
         requestAnimationFrame(() => {
             messagesContainer.scrollTop = messagesContainer.scrollHeight;
         });
         
         return loadingDiv;
     }
     
     removeModernLoadingMessage() {
         const loadingMessage = document.getElementById('modern-loading-message');
         if (loadingMessage) {
             loadingMessage.remove();
         }
     }
     
     // 兼容旧方法
     showLoadingMessage() { return this.showModernLoadingMessage(); }
     removeLoadingMessage() { this.removeModernLoadingMessage(); }
    
              async getAIResponse(userMessage) {
         if (!this.config.apiKey) {
             this.addMessage('抱歉，AI助手还未配置API密钥。请联系管理员配置。', 'assistant');
             return;
         }
         
         this.isLoading = true;
         const modernSendBtn = this.assistantElement.querySelector('.modern-send-btn');
         if (modernSendBtn) modernSendBtn.disabled = true;
        
        const loadingMessage = this.showLoadingMessage();
        
        try {
            // 构建系统提示词
            const systemPrompt = this.buildSystemPrompt();
            
            // 构建包含当前学习状态的上下文信息
            const contextualInfo = this.buildContextualPrompt(userMessage);
            
            // 构建消息历史，在用户消息中添加上下文信息
            const enhancedUserMessage = contextualInfo ? 
                `${contextualInfo}\n\n学生问题：${userMessage}` : userMessage;
            
            const messages = [
                { role: 'system', content: systemPrompt },
                ...this.conversationHistory.slice(-10), // 只保留最近10条对话
                { role: 'user', content: enhancedUserMessage }
            ];
            
            console.log('发送AI请求:', {
                url: this.config.apiUrl,
                model: this.config.model,
                messages: messages.length,
                apiKeyConfigured: !!this.config.apiKey
            });
            
            const response = await fetch(this.config.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.apiKey}`
                },
                body: JSON.stringify({
                    model: this.config.model,
                    messages: messages,
                    temperature: 0.7,
                    max_tokens: 500,
                    stream: false
                })
            });
            
            console.log('API响应状态:', response.status);
            
            if (!response.ok) {
                const errorText = await response.text();
                console.error('API错误响应:', errorText);
                throw new Error(`API请求失败: ${response.status} - ${errorText}`);
            }
            
            const data = await response.json();
            console.log('API响应数据:', data);
            
            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('API响应格式不正确');
            }
            
            const aiResponse = data.choices[0].message.content;
            
            this.removeLoadingMessage();
            this.addMessage(aiResponse, 'assistant');
            
        } catch (error) {
            console.error('AI助手响应错误:', error);
            this.removeLoadingMessage();
            this.addMessage(`抱歉，我现在无法回答您的问题。错误信息：${error.message}`, 'assistant');
        } finally {
            this.isLoading = false;
            if (modernSendBtn) modernSendBtn.disabled = false;
        }
     }
    
         buildSystemPrompt() {
         const currentPage = window.location.pathname;
         const currentUrl = window.location.href;
         console.log('buildSystemPrompt - 当前页面路径:', currentPage);
         console.log('buildSystemPrompt - 页面URL:', currentUrl);
         
         let contextInfo = '';
         let specificContent = '';
         
         // 使用URL而不是路径进行匹配，以兼容不同的文件路径格式
         if (currentUrl.includes('数据隐患辨一辨') || currentPage.includes('数据隐患辨一辨')) {
             console.log('识别为：数据隐患辨一辨页面');
            contextInfo = '当前用户正在学习"数据隐患辨一辨"，这是一个通过拖拽游戏学习识别数据安全隐患和安全行为的练习。';
            specificContent = `
具体学习内容包括：
- 隐患陷阱：👤陌生人添加好友、📧陌生邮件链接、📱来历不明二维码、🎁花1分钱领礼品、📦随意丢弃快递盒、👾低价领取游戏皮肤
- 安全行为：🛡️安装杀毒软件、🔒电脑设置登录密码

你需要帮助学生理解：
1. 为什么这些行为是危险的或安全的
2. 如何在生活中识别类似情况
3. 正确的应对方法
4. 鼓励学生完成分类游戏`;
                 } else if (currentUrl.includes('数据分享试一试') || currentPage.includes('数据分享试一试')) {
             console.log('识别为：数据分享试一试页面');
             contextInfo = '当前用户正在学习"数据分享试一试"，这是一个模拟朋友圈发布的练习。';
            specificContent = `
具体学习内容包括：
- 朋友圈发布模拟器，包含小智的相册照片
- 隐私设置：谁可以看、位置信息、陌生人查看权限
- 危险信息识别：飞机票、护照、酒店位置等个人敏感信息
- 安全分享原则：保护个人隐私、避免泄露位置等

你需要帮助学生：
1. 识别哪些照片可以安全分享
2. 理解隐私设置的重要性
3. 学会保护个人敏感信息
4. 指导正确的朋友圈发布操作`;
                 } else if (currentUrl.includes('数据备份存一存') || currentPage.includes('数据备份存一存')) {
             console.log('识别为：数据备份存一存页面');
             contextInfo = '当前用户正在学习"数据备份存一存"，包含视频学习和实践操作两部分。';
            specificContent = `
具体学习内容包括：
- 观看数据备份重要性的教学视频
- 模拟备份操作：从"小智的文件"中选择重要文件备份到"移动硬盘"
- 文件分类：📁旅游照片(重要)、📁临时下载、🎮游戏程序、📁缓存文件、📁系统临时文件等
- 学习右键复制粘贴操作

你需要帮助学生：
1. 理解数据备份的重要性
2. 识别哪些文件需要备份(重要文件vs临时文件)
3. 学会基本的文件操作方法
4. 养成定期备份的好习惯`;
                 } else if (currentUrl.includes('保护措施选一选') || currentPage.includes('保护措施选一选')) {
             console.log('识别为：保护措施选一选页面');
             contextInfo = '当前用户正在学习"保护措施选一选"，这是一个拖拽匹配练习。';
            specificContent = `
具体学习内容包括：
- 数据处理活动：数据收集、数据存储、数据共享、数据传输
- 保护措施选项：使用自己的流量、定期备份在U盘、销毁、谁可以看、慎重填写、尽可能少提供信息
- 拖拽匹配操作：将保护措施拖拽到对应的数据处理活动区域
- 正确匹配关系：
  * 数据收集 → 慎重填写、尽可能少提供信息
  * 数据存储 → 定期备份在U盘、销毁
  * 数据共享 → 谁可以看
  * 数据传输 → 使用自己的流量

你需要帮助学生：
1. 理解每种数据处理活动的具体含义和场景
2. 学会为每种活动选择最合适的保护措施
3. 解释为什么这样匹配是正确的（提供具体原因）
4. 指导拖拽操作的具体步骤
5. 当学生匹配错误时，耐心解释正确的匹配逻辑`;
                 } else {
             console.log('识别为：主页面或未知页面');
             contextInfo = '用户正在数据安全小卫士闯关乐园的主页面，这是一个面向小学四年级学生的数据安全教育项目。';
            specificContent = `
整体学习内容包括四个闯关：
1. 🔍 数据隐患辨一辨 - 识别安全隐患
2. 📱 数据分享试一试 - 安全分享练习  
3. 💾 数据备份存一存 - 备份重要数据
4. 🛡️ 保护措施选一选 - 学习保护方法

你需要帮助学生了解整体学习目标和各关卡内容。`;
        }
        
        return `你是"${this.config.name}"，专门帮助小学四年级学生学习当前页面具体内容的AI助手。

【当前学习环境】${contextInfo}

【具体学习内容】${specificContent}

【你的任务】
1. 紧密结合当前页面的具体学习内容回答问题
2. 引用页面中的具体元素、操作和概念
3. 提供针对当前学习任务的具体指导
4. 帮助学生理解页面中每个操作的意义

【回答要求】
- 语言简单易懂，适合小学四年级学生
- 直接针对页面内容，不泛泛而谈
- 多用具体例子说明（引用页面中的实际内容）
- 鼓励学生动手操作和思考
- 回答控制在100字以内，多用emoji

【特别注意】
- 必须结合页面的具体学习内容回答
- 如果学生问题偏离当前学习内容，要引导回到页面学习任务
- 优先解答与当前操作相关的问题

请根据学生的具体问题，结合当前页面的学习内容给出针对性回答。`;
    }
    
         // 显示通知点（可用于提醒用户有新功能或重要提示）
     showNotification() {
         if (!this.isOpen) {
             const notificationDot = this.assistantElement.querySelector('.notification-dot');
             const messageCount = this.assistantElement.querySelector('.message-count');
             
             if (notificationDot) {
                 notificationDot.style.display = 'block';
             }
             
             if (messageCount) {
                 messageCount.style.display = 'flex';
                 const currentCount = parseInt(messageCount.textContent) || 0;
                 messageCount.textContent = currentCount + 1;
             }
         }
     }
    
         // 获取当前页面的学习状态
     getCurrentLearningState() {
         const currentPage = window.location.pathname;
         const currentUrl = window.location.href;
         let state = {};
         
         if (currentUrl.includes('数据隐患辨一辨') || currentPage.includes('数据隐患辨一辨')) {
            // 获取分类游戏状态
            const dangerItems = document.querySelectorAll('#danger-items .dropped-item');
            const safeItems = document.querySelectorAll('#safe-items .dropped-item');
            const remainingItems = document.querySelectorAll('.item:not([style*="display: none"])');
            
            state = {
                dangerClassified: dangerItems.length,
                safeClassified: safeItems.length,
                remaining: remainingItems.length,
                completed: remainingItems.length === 0
            };
        } else if (currentUrl.includes('数据分享试一试') || currentPage.includes('数据分享试一试')) {
            // 获取朋友圈发布状态
            const textContent = document.querySelector('textarea')?.value || '';
            const imageCount = document.querySelectorAll('.image-preview').length;
            const locationSet = document.querySelector('#location')?.value || '';
            const privacySet = document.querySelector('#visibility')?.value || '';
            
            state = {
                hasText: textContent.length > 0,
                imageCount: imageCount,
                locationSet: locationSet !== '',
                privacySet: privacySet,
                readyToPublish: textContent.length > 0 || imageCount > 0
            };
        } else if (currentUrl.includes('数据备份存一存') || currentPage.includes('数据备份存一存')) {
            // 获取备份操作状态
            const backupFiles = document.querySelectorAll('#backupFiles .file-item');
            const videoEnded = document.querySelector('#myVideo')?.ended || false;
            const nextPageVisible = !document.querySelector('#nextPage')?.classList.contains('hidden');
            
            state = {
                videoWatched: videoEnded,
                practiceStarted: nextPageVisible,
                backedUpCount: backupFiles.length,
                completed: document.querySelector('#completeBtn')?.style.display !== 'none'
            };
        } else if (currentUrl.includes('保护措施选一选') || currentPage.includes('保护措施选一选')) {
            // 获取保护措施匹配状态
            const dropzones = document.querySelectorAll('.dropzone');
            const availableOptions = document.querySelectorAll('.option:not(.used)');
            let filledZones = 0;
            let totalMatches = 0;
            let matchDetails = {};
            
            dropzones.forEach(zone => {
                const activity = zone.getAttribute('data-activity') || zone.textContent.trim();
                const matches = zone.querySelectorAll('.dropped-option');
                if (matches.length > 0) {
                    filledZones++;
                    matchDetails[activity] = Array.from(matches).map(m => m.textContent.trim());
                }
                totalMatches += matches.length;
            });
            
            state = {
                filledZones: filledZones,
                totalZones: dropzones.length,
                totalMatches: totalMatches,
                availableOptions: availableOptions.length,
                matchDetails: matchDetails,
                completed: filledZones === dropzones.length && availableOptions.length === 0
            };
        }
        
        return state;
    }
    
         // 根据学习状态生成上下文信息
     buildContextualPrompt(userMessage) {
         const learningState = this.getCurrentLearningState();
         const currentPage = window.location.pathname;
         const currentUrl = window.location.href;
         let contextualInfo = '';
         
         if (currentUrl.includes('数据隐患辨一辨') || currentPage.includes('数据隐患辨一辨')) {
            contextualInfo = `
当前游戏状态：
- 已分类到隐患陷阱：${learningState.dangerClassified}个
- 已分类到安全行为：${learningState.safeClassified}个  
- 剩余未分类：${learningState.remaining}个
- 游戏${learningState.completed ? '已完成' : '进行中'}

请根据当前状态和学生问题，提供具体的操作指导。`;
        } else if (currentUrl.includes('数据分享试一试') || currentPage.includes('数据分享试一试')) {
            contextualInfo = `
当前发布状态：
- 文字内容：${learningState.hasText ? '已填写' : '未填写'}
- 图片数量：${learningState.imageCount}张
- 位置设置：${learningState.locationSet ? '已设置' : '未设置'}
- 隐私设置：${learningState.privacySet}
- 发布准备：${learningState.readyToPublish ? '可以发布' : '内容不足'}

请结合当前状态指导学生安全发布朋友圈。`;
        } else if (currentUrl.includes('数据备份存一存') || currentPage.includes('数据备份存一存')) {
            contextualInfo = `
当前学习状态：
- 视频学习：${learningState.videoWatched ? '已观看' : '观看中'}
- 实践操作：${learningState.practiceStarted ? '已开始' : '未开始'}
- 已备份文件：${learningState.backedUpCount}个
- 备份任务：${learningState.completed ? '已完成' : '进行中'}

请根据当前进度提供针对性指导。`;
        } else if (currentUrl.includes('保护措施选一选') || currentPage.includes('保护措施选一选')) {
            let matchDetailsText = '';
            if (learningState.matchDetails) {
                Object.entries(learningState.matchDetails).forEach(([activity, measures]) => {
                    matchDetailsText += `\n- ${activity}：已匹配 ${measures.join('、')}`;
                });
            }
            
            contextualInfo = `
当前匹配状态：
- 已完成匹配：${learningState.filledZones}/${learningState.totalZones}个数据处理活动
- 剩余可选措施：${learningState.availableOptions}个
- 总匹配数量：${learningState.totalMatches}个
- 任务状态：${learningState.completed ? '已完成所有匹配' : '匹配进行中'}
${matchDetailsText}

请根据当前匹配进度和具体的数据处理活动，提供针对性的操作指导和知识解释。`;
        }
        
        return contextualInfo;
    }
    
    // 更新助手配置
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        // 更新界面显示
        if (newConfig.name) {
            this.assistantElement.querySelector('.assistant-name').textContent = newConfig.name;
        }
        if (newConfig.avatar) {
            this.assistantElement.querySelector('.avatar-icon').textContent = newConfig.avatar;
            this.assistantElement.querySelectorAll('.message-avatar').forEach(avatar => {
                if (!avatar.closest('.user-message')) {
                    avatar.textContent = newConfig.avatar;
                }
            });
        }
    }
    
    // 销毁助手（页面切换时清理）
    destroy() {
        if (this.assistantElement) {
            this.assistantElement.remove();
        }
    }
}

// 全局函数，方便在页面中快速初始化
window.initAIAssistant = function(config = {}) {
    // 如果已存在助手，先销毁
    if (window.aiAssistant) {
        window.aiAssistant.destroy();
    }
    
    // 创建新的助手实例
    window.aiAssistant = new AIAssistant(config);
    
    // 确保快捷问题正确生成
    setTimeout(() => {
        if (window.aiAssistant && window.aiAssistant.generateQuickQuestions) {
            console.log('强制刷新快捷问题，当前URL:', window.location.href);
            window.aiAssistant.generateQuickQuestions();
        }
    }, 200);
    
    // 再次确保
    setTimeout(() => {
        if (window.aiAssistant && window.aiAssistant.generateQuickQuestions) {
            window.aiAssistant.generateQuickQuestions();
        }
    }, 500);
    
    return window.aiAssistant;
};

// 兼容旧的函数名
window.initPageAIAssistant = function(pageConfig = {}) {
    console.log('=== initPageAIAssistant 被调用 ===');
    console.log('传入的页面配置:', pageConfig);
    console.log('当前页面完整URL:', window.location.href);
    console.log('当前页面路径:', window.location.pathname);
    
    // 根据当前页面确定配置键
    const currentPage = window.location.pathname;
    const currentUrl = window.location.href;
    let configKey = 'main';
    
    if (currentUrl.includes('数据隐患辨一辨') || currentPage.includes('数据隐患辨一辨')) {
        configKey = 'level1';
        console.log('页面匹配：数据隐患辨一辨 -> level1');
    } else if (currentUrl.includes('数据分享试一试') || currentPage.includes('数据分享试一试')) {
        configKey = 'level2';
        console.log('页面匹配：数据分享试一试 -> level2');
    } else if (currentUrl.includes('数据备份存一存') || currentPage.includes('数据备份存一存')) {
        configKey = 'level3';
        console.log('页面匹配：数据备份存一存 -> level3');
    } else if (currentUrl.includes('保护措施选一选') || currentPage.includes('保护措施选一选')) {
        configKey = 'level4';
        console.log('页面匹配：保护措施选一选 -> level4');
    } else {
        console.log('页面匹配：默认主页 -> main');
    }
    
    // 检查配置是否存在
    console.log('可用的配置键:', Object.keys(window.AI_ASSISTANT_CONFIGS || {}));
    
    // 合并页面特定的配置
    const baseConfig = window.AI_ASSISTANT_CONFIGS?.[configKey] || {};
    const config = {
        ...baseConfig,
        ...pageConfig
    };
    
    console.log('选择的配置键:', configKey);
    console.log('基础配置:', baseConfig);
    console.log('最终合并配置:', config);
    console.log('=== initPageAIAssistant 配置完成 ===');
    
    return window.initAIAssistant(config);
};

// 全局调试方法
window.forceRefreshQuickQuestions = function() {
    if (window.aiAssistant && window.aiAssistant.generateQuickQuestions) {
        console.log('手动强制刷新快捷问题');
        window.aiAssistant.generateQuickQuestions();
        return '快捷问题已刷新';
    } else {
        return 'AI助手未找到';
    }
};

// 导出类（如果使用模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIAssistant;
}

