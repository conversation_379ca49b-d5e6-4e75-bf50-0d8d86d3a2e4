<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习评价报告生成器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .report-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .student-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #667eea;
        }

        .overall-score {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .stars {
            font-size: 1.5rem;
            margin: 10px 0;
        }

        .skills-preview {
            margin: 15px 0;
        }

        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 8px 12px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .skill-score {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .skill-score.excellent {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }

        .skill-score.good {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .skill-score.needs-improvement {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        .highlights {
            margin-top: 15px;
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            max-width: 700px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.4s ease-out;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px 30px;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: white;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 30px;
            line-height: 1.6;
        }

        @keyframes modalSlideIn {
            from { 
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                width: 95%;
                max-height: 85vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> 学习评价报告生成器</h1>
            <p>智能生成个性化学习评价报告，展示学生的学习成果和进步</p>
        </div>

        <div class="content">
            <div class="controls">
                <button class="btn btn-primary" onclick="generateRandomReport()">
                    <i class="fas fa-magic"></i> 生成随机报告
                </button>
                <button class="btn btn-secondary" onclick="showAllReports()">
                    <i class="fas fa-list"></i> 查看所有报告
                </button>
                <button class="btn btn-secondary" onclick="exportReports()">
                    <i class="fas fa-download"></i> 导出报告
                </button>
                <a href="AI智能评价系统.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回评价系统
                </a>
            </div>

            <div class="reports-grid" id="reportsGrid">
                <!-- 报告卡片将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 详细报告模态框 -->
    <div class="modal" id="reportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">详细学习报告</h2>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详细报告内容将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 优秀学习报告数据
        const excellentReports = [
            {
                studentName: "小明",
                overallScore: 95,
                stars: 5,
                skills: {
                    "隐患识别": { score: 92, level: "优秀" },
                    "安全分享": { score: 95, level: "优秀" },
                    "数据备份": { score: 88, level: "优秀" },
                    "保护措施": { score: 96, level: "优秀" }
                },
                highlights: [
                    "回答详细认真，思考深入",
                    "具备优秀的安全意识",
                    "能够举例说明，理解透彻",
                    "逻辑思维清晰，有条理"
                ],
                detailedReport: `
                    <h3>🌟 整体表现</h3>
                    <p>⭐⭐⭐⭐⭐ (95分) - 表现优异！</p>

                    <h3>✨ 学习亮点</h3>
                    <ul>
                        <li>在隐患识别环节表现突出，能够准确识别各种网络安全风险</li>
                        <li>对数据分享的安全性有深刻理解，知道如何保护个人隐私</li>
                        <li>掌握了完整的数据备份策略，能够举出具体的实施方案</li>
                        <li>在保护措施方面展现了专业水准，提出了多种有效的防护方法</li>
                    </ul>

                    <h3>🎯 技能掌握详情</h3>
                    <p><strong>隐患识别 (92分)：</strong> 能够快速识别钓鱼网站、恶意链接等常见威胁，具备敏锐的安全嗅觉。</p>
                    <p><strong>安全分享 (95分)：</strong> 深刻理解社交媒体分享的风险，能够制定合理的隐私保护策略。</p>
                    <p><strong>数据备份 (88分)：</strong> 掌握多种备份方法，理解备份的重要性和最佳实践。</p>
                    <p><strong>保护措施 (96分)：</strong> 能够综合运用各种安全工具和策略，构建完整的防护体系。</p>

                    <h3>💡 学习建议</h3>
                    <ul>
                        <li>继续保持这种优秀的学习态度和安全意识</li>
                        <li>可以尝试学习更高级的网络安全知识</li>
                        <li>建议向同学分享你的安全知识，成为安全小导师</li>
                    </ul>

                    <h3>🎉 总结</h3>
                    <p>恭喜你！你已经成为了一名真正的数据安全专家。你的表现超出了预期，不仅掌握了所有核心知识点，还能够灵活运用到实际场景中。继续保持这种学习热情，你一定会在网络安全领域有更大的成就！</p>
                `
            },
            {
                studentName: "小红",
                overallScore: 92,
                stars: 5,
                skills: {
                    "隐患识别": { score: 90, level: "优秀" },
                    "安全分享": { score: 94, level: "优秀" },
                    "数据备份": { score: 89, level: "优秀" },
                    "保护措施": { score: 93, level: "优秀" }
                },
                highlights: [
                    "安全意识强，能主动思考",
                    "善于结合实际生活场景",
                    "对隐私保护有独到见解",
                    "学习态度积极主动"
                ],
                detailedReport: `
                    <h3>🌟 整体表现</h3>
                    <p>⭐⭐⭐⭐⭐ (92分) - 表现卓越！</p>

                    <h3>✨ 学习亮点</h3>
                    <ul>
                        <li>在安全分享方面表现特别突出，对社交媒体隐私设置有深入理解</li>
                        <li>能够将学到的知识与日常生活紧密结合，实用性强</li>
                        <li>对网络诈骗手段有清晰认识，防范意识很强</li>
                        <li>在小组讨论中积极发言，乐于分享安全知识</li>
                    </ul>

                    <h3>🎯 技能掌握详情</h3>
                    <p><strong>隐患识别 (90分)：</strong> 能够识别大部分常见网络威胁，对新型诈骗手段保持警惕。</p>
                    <p><strong>安全分享 (94分)：</strong> 在社交媒体使用方面表现优异，深知过度分享的风险。</p>
                    <p><strong>数据备份 (89分)：</strong> 理解备份的重要性，能够制定个人数据保护计划。</p>
                    <p><strong>保护措施 (93分)：</strong> 掌握多种防护手段，能够根据不同场景选择合适的保护策略。</p>

                    <h3>💡 学习建议</h3>
                    <ul>
                        <li>可以进一步学习高级的隐私保护技术</li>
                        <li>建议关注最新的网络安全动态</li>
                        <li>可以考虑参加网络安全相关的竞赛或活动</li>
                    </ul>

                    <h3>🎉 总结</h3>
                    <p>你在数据安全学习中表现非常出色！特别是在实际应用方面，你能够很好地将理论知识转化为实践行动。你的安全意识和防范能力已经达到了很高的水平，继续保持这种学习状态，你一定能成为网络安全领域的佼佼者！</p>
                `
            },
            {
                studentName: "小华",
                overallScore: 89,
                stars: 4,
                skills: {
                    "隐患识别": { score: 87, level: "优秀" },
                    "安全分享": { score: 85, level: "优秀" },
                    "数据备份": { score: 92, level: "优秀" },
                    "保护措施": { score: 91, level: "优秀" }
                },
                highlights: [
                    "在数据备份方面表现突出",
                    "技术理解能力强",
                    "善于提出创新性解决方案",
                    "学习进步明显"
                ],
                detailedReport: `
                    <h3>🌟 整体表现</h3>
                    <p>⭐⭐⭐⭐ (89分) - 表现优秀！</p>

                    <h3>✨ 学习亮点</h3>
                    <ul>
                        <li>在数据备份领域展现了专业水准，提出了多种创新的备份策略</li>
                        <li>对技术细节有深入理解，能够解释复杂的安全概念</li>
                        <li>学习过程中进步显著，从基础到精通的转变很明显</li>
                        <li>善于思考和提问，经常能提出有价值的问题</li>
                    </ul>

                    <h3>🎯 技能掌握详情</h3>
                    <p><strong>隐患识别 (87分)：</strong> 具备良好的威胁识别能力，对常见攻击手段有清晰认识。</p>
                    <p><strong>安全分享 (85分)：</strong> 理解分享风险，能够制定合理的隐私保护策略。</p>
                    <p><strong>数据备份 (92分)：</strong> 在这个领域表现突出，掌握了多种备份技术和最佳实践。</p>
                    <p><strong>保护措施 (91分)：</strong> 能够综合运用各种安全工具，构建有效的防护体系。</p>

                    <h3>💡 学习建议</h3>
                    <ul>
                        <li>可以在隐患识别方面加强练习</li>
                        <li>建议多参与实际的安全项目</li>
                        <li>继续发挥在数据备份方面的优势</li>
                    </ul>

                    <h3>🎉 总结</h3>
                    <p>你在数据安全学习中表现优秀，特别是在技术理解和数据备份方面有突出表现。你的学习能力很强，进步速度令人印象深刻。继续保持这种学习热情和技术钻研精神，你一定能在网络安全领域取得更大的成就！</p>
                `
            },
            {
                studentName: "小李",
                overallScore: 94,
                stars: 5,
                skills: {
                    "隐患识别": { score: 96, level: "优秀" },
                    "安全分享": { score: 91, level: "优秀" },
                    "数据备份": { score: 93, level: "优秀" },
                    "保护措施": { score: 95, level: "优秀" }
                },
                highlights: [
                    "隐患识别能力超强",
                    "反应敏捷，思维活跃",
                    "能够举一反三",
                    "团队合作能力强"
                ],
                detailedReport: `
                    <h3>🌟 整体表现</h3>
                    <p>⭐⭐⭐⭐⭐ (94分) - 表现卓越！</p>

                    <h3>✨ 学习亮点</h3>
                    <ul>
                        <li>在隐患识别方面表现极为突出，几乎能识别所有类型的网络威胁</li>
                        <li>反应速度快，能够迅速判断安全风险</li>
                        <li>具备举一反三的能力，能从一个案例推导出多种应用场景</li>
                        <li>在团队学习中起到了很好的带头作用</li>
                    </ul>

                    <h3>🎯 技能掌握详情</h3>
                    <p><strong>隐患识别 (96分)：</strong> 在这个领域表现极为出色，对各种网络威胁有敏锐的洞察力。</p>
                    <p><strong>安全分享 (91分)：</strong> 能够很好地平衡分享与隐私保护的关系。</p>
                    <p><strong>数据备份 (93分)：</strong> 掌握了完整的备份策略，能够制定个性化的备份方案。</p>
                    <p><strong>保护措施 (95分)：</strong> 能够灵活运用各种防护手段，构建多层次的安全防护体系。</p>

                    <h3>💡 学习建议</h3>
                    <ul>
                        <li>可以考虑深入学习网络安全技术</li>
                        <li>建议参与更多的实战演练</li>
                        <li>可以尝试指导其他同学学习</li>
                    </ul>

                    <h3>🎉 总结</h3>
                    <p>你在数据安全学习中表现非常优异！你的隐患识别能力特别突出，这是网络安全的核心技能。你不仅学会了知识，更重要的是培养了安全思维。继续发挥你的优势，你有潜力成为网络安全领域的专家！</p>
                `
            },
            {
                studentName: "小张",
                overallScore: 91,
                stars: 5,
                skills: {
                    "隐患识别": { score: 88, level: "优秀" },
                    "安全分享": { score: 93, level: "优秀" },
                    "数据备份": { score: 90, level: "优秀" },
                    "保护措施": { score: 94, level: "优秀" }
                },
                highlights: [
                    "保护措施应用能力强",
                    "实践操作能力突出",
                    "安全意识深入人心",
                    "学习方法科学有效"
                ],
                detailedReport: `
                    <h3>🌟 整体表现</h3>
                    <p>⭐⭐⭐⭐⭐ (91分) - 表现优异！</p>

                    <h3>✨ 学习亮点</h3>
                    <ul>
                        <li>在保护措施应用方面表现突出，能够熟练使用各种安全工具</li>
                        <li>实践操作能力强，理论与实践结合得很好</li>
                        <li>安全意识已经深入到日常生活的方方面面</li>
                        <li>学习方法科学，善于总结和归纳</li>
                    </ul>

                    <h3>🎯 技能掌握详情</h3>
                    <p><strong>隐患识别 (88分)：</strong> 具备良好的威胁识别能力，能够发现大部分安全隐患。</p>
                    <p><strong>安全分享 (93分)：</strong> 在社交媒体安全使用方面表现优秀，隐私保护意识强。</p>
                    <p><strong>数据备份 (90分)：</strong> 掌握了系统的备份方法，能够制定完整的数据保护计划。</p>
                    <p><strong>保护措施 (94分)：</strong> 在这个领域表现最为突出，能够灵活运用各种防护技术。</p>

                    <h3>💡 学习建议</h3>
                    <ul>
                        <li>可以在隐患识别方面进一步提升</li>
                        <li>建议多参与安全技术的实践项目</li>
                        <li>继续发挥在保护措施方面的优势</li>
                    </ul>

                    <h3>🎉 总结</h3>
                    <p>你在数据安全学习中表现优异，特别是在实践应用方面有很强的能力。你不仅掌握了理论知识，更重要的是能够将这些知识转化为实际的安全防护能力。继续保持这种理论与实践相结合的学习方式，你一定能在网络安全领域有所建树！</p>
                `
            }
        ];

        // 随机姓名池
        const randomNames = ["小王", "小刘", "小陈", "小杨", "小赵", "小孙", "小周", "小吴", "小郑", "小冯"];

        // 页面加载时初始化
        window.onload = function() {
            loadExcellentReports();
        };

        // 加载优秀报告
        function loadExcellentReports() {
            showAllReports();
        }

        // 显示所有报告
        function showAllReports() {
            const grid = document.getElementById('reportsGrid');
            grid.innerHTML = '';

            excellentReports.forEach((report, index) => {
                const card = createReportCard(report, index);
                grid.appendChild(card);
            });
        }

        // 创建报告卡片
        function createReportCard(report, index) {
            const card = document.createElement('div');
            card.className = 'report-card';
            card.onclick = () => showDetailedReport(report);

            const skillsHtml = Object.entries(report.skills).map(([skill, data]) => {
                const scoreClass = data.level === '优秀' ? 'excellent' :
                                 data.level === '良好' ? 'good' : 'needs-improvement';
                return `
                    <div class="skill-item">
                        <span>${skill}</span>
                        <span class="skill-score ${scoreClass}">${data.score}分</span>
                    </div>
                `;
            }).join('');

            const highlightsHtml = report.highlights.slice(0, 2).map(highlight =>
                `• ${highlight}`
            ).join('<br>');

            card.innerHTML = `
                <div class="report-header">
                    <div class="student-name">${report.studentName}</div>
                    <div class="overall-score">${report.overallScore}分</div>
                </div>
                <div class="stars">${'⭐'.repeat(report.stars)}</div>
                <div class="skills-preview">
                    ${skillsHtml}
                </div>
                <div class="highlights">
                    ${highlightsHtml}
                    ${report.highlights.length > 2 ? '<br>...' : ''}
                </div>
            `;

            return card;
        }

        // 显示详细报告
        function showDetailedReport(report) {
            const modal = document.getElementById('reportModal');
            const title = document.getElementById('modalTitle');
            const body = document.getElementById('modalBody');

            title.textContent = `${report.studentName}的详细学习报告`;
            body.innerHTML = report.detailedReport;

            modal.classList.add('show');

            // 添加点击背景关闭功能
            modal.onclick = function(event) {
                if (event.target === modal) {
                    closeModal();
                }
            };

            // 添加ESC键关闭功能
            document.addEventListener('keydown', handleEscKey);
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('reportModal');
            modal.classList.remove('show');
            document.removeEventListener('keydown', handleEscKey);
        }

        // 处理ESC键
        function handleEscKey(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        }

        // 生成随机报告
        function generateRandomReport() {
            const randomName = randomNames[Math.floor(Math.random() * randomNames.length)];
            const baseScore = 85 + Math.floor(Math.random() * 15); // 85-99分
            const stars = baseScore >= 95 ? 5 : baseScore >= 85 ? 4 : 3;

            // 生成随机技能分数
            const skills = {
                "隐患识别": {
                    score: Math.max(80, baseScore + Math.floor(Math.random() * 10) - 5),
                    level: "优秀"
                },
                "安全分享": {
                    score: Math.max(80, baseScore + Math.floor(Math.random() * 10) - 5),
                    level: "优秀"
                },
                "数据备份": {
                    score: Math.max(80, baseScore + Math.floor(Math.random() * 10) - 5),
                    level: "优秀"
                },
                "保护措施": {
                    score: Math.max(80, baseScore + Math.floor(Math.random() * 10) - 5),
                    level: "优秀"
                }
            };

            // 随机选择亮点
            const allHighlights = [
                "回答详细认真，思考深入",
                "具备优秀的安全意识",
                "能够举例说明，理解透彻",
                "逻辑思维清晰，有条理",
                "安全意识强，能主动思考",
                "善于结合实际生活场景",
                "对隐私保护有独到见解",
                "学习态度积极主动",
                "技术理解能力强",
                "善于提出创新性解决方案",
                "学习进步明显",
                "实践操作能力突出"
            ];

            const selectedHighlights = [];
            for (let i = 0; i < 4; i++) {
                const randomHighlight = allHighlights[Math.floor(Math.random() * allHighlights.length)];
                if (!selectedHighlights.includes(randomHighlight)) {
                    selectedHighlights.push(randomHighlight);
                }
            }

            const newReport = {
                studentName: randomName,
                overallScore: baseScore,
                stars: stars,
                skills: skills,
                highlights: selectedHighlights,
                detailedReport: generateDetailedReport(randomName, baseScore, skills, selectedHighlights)
            };

            // 添加到报告列表
            excellentReports.unshift(newReport);

            // 重新显示所有报告
            showAllReports();

            // 显示生成成功提示
            showNotification(`成功生成${randomName}的学习报告！`);
        }

        // 生成详细报告内容
        function generateDetailedReport(name, score, skills, highlights) {
            const stars = score >= 95 ? 5 : score >= 85 ? 4 : 3;
            const starsDisplay = '⭐'.repeat(stars);

            let report = `
                <h3>🌟 整体表现</h3>
                <p>${starsDisplay} (${score}分) - 表现${score >= 95 ? '优异' : score >= 85 ? '优秀' : '良好'}！</p>

                <h3>✨ 学习亮点</h3>
                <ul>
            `;

            highlights.forEach(highlight => {
                report += `<li>${highlight}</li>`;
            });

            report += `
                </ul>

                <h3>🎯 技能掌握详情</h3>
            `;

            Object.entries(skills).forEach(([skill, data]) => {
                report += `<p><strong>${skill} (${data.score}分)：</strong> ${getSkillDescription(skill, data.score)}</p>`;
            });

            report += `
                <h3>💡 学习建议</h3>
                <ul>
                    <li>继续保持优秀的学习态度和安全意识</li>
                    <li>可以尝试学习更高级的网络安全知识</li>
                    <li>建议向同学分享你的安全知识，成为安全小导师</li>
                </ul>

                <h3>🎉 总结</h3>
                <p>恭喜${name}！你在数据安全学习中表现${score >= 95 ? '非常优异' : score >= 85 ? '优秀' : '良好'}，已经具备了扎实的网络安全基础。继续保持这种学习热情，你一定能在网络安全领域取得更大的成就！</p>
            `;

            return report;
        }

        // 获取技能描述
        function getSkillDescription(skill, score) {
            const descriptions = {
                "隐患识别": {
                    high: "能够快速识别各种网络安全威胁，具备敏锐的安全嗅觉",
                    medium: "具备良好的威胁识别能力，对常见攻击手段有清晰认识",
                    low: "基本掌握了隐患识别的方法，需要继续加强练习"
                },
                "安全分享": {
                    high: "深刻理解社交媒体分享的风险，能够制定合理的隐私保护策略",
                    medium: "理解分享风险，能够在大部分情况下保护个人隐私",
                    low: "了解基本的分享安全原则，需要进一步提升隐私保护意识"
                },
                "数据备份": {
                    high: "掌握了完整的备份策略，能够制定个性化的数据保护方案",
                    medium: "理解备份的重要性，掌握了基本的备份方法",
                    low: "了解数据备份的基本概念，需要学习更多实践技巧"
                },
                "保护措施": {
                    high: "能够综合运用各种安全工具和策略，构建完整的防护体系",
                    medium: "掌握了多种防护手段，能够根据情况选择合适的保护策略",
                    low: "了解基本的保护措施，需要加强实际应用能力"
                }
            };

            const level = score >= 90 ? 'high' : score >= 80 ? 'medium' : 'low';
            return descriptions[skill][level];
        }

        // 显示通知
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
                z-index: 2000;
                font-weight: 600;
                animation: slideInRight 0.3s ease-out;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 导出报告
        function exportReports() {
            const reportData = excellentReports.map(report => ({
                学生姓名: report.studentName,
                总体分数: report.overallScore,
                星级评价: report.stars,
                隐患识别: report.skills["隐患识别"].score,
                安全分享: report.skills["安全分享"].score,
                数据备份: report.skills["数据备份"].score,
                保护措施: report.skills["保护措施"].score,
                学习亮点: report.highlights.join('; ')
            }));

            const csvContent = convertToCSV(reportData);
            downloadCSV(csvContent, '学习评价报告.csv');

            showNotification('报告导出成功！');
        }

        // 转换为CSV格式
        function convertToCSV(data) {
            if (data.length === 0) return '';

            const headers = Object.keys(data[0]);
            const csvRows = [headers.join(',')];

            data.forEach(row => {
                const values = headers.map(header => {
                    const value = row[header];
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                });
                csvRows.push(values.join(','));
            });

            return csvRows.join('\n');
        }

        // 下载CSV文件
        function downloadCSV(csvContent, filename) {
            const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
