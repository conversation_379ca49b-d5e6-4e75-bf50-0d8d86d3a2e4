<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习评价报告生成器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .report-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .student-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #667eea;
        }

        .overall-score {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .stars {
            font-size: 1.5rem;
            margin: 10px 0;
        }

        .skills-preview {
            margin: 15px 0;
        }

        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 8px 12px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .skill-score {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .skill-score.excellent {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }

        .skill-score.good {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .skill-score.needs-improvement {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        .highlights {
            margin-top: 15px;
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .modal-content {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.4s ease-out;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px 20px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 20px 20px 0 0;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #666;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
            color: #333;
        }

        .modal-body {
            padding: 30px;
            line-height: 1.6;
            color: #333;
        }

        /* 原始报告样式 */
        .report-content {
            font-size: 16px;
        }

        .report-content h3 {
            color: #667eea;
            margin: 20px 0 10px 0;
            font-size: 1.2rem;
        }

        .report-content ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .report-content li {
            margin: 8px 0;
        }

        .report-stars {
            font-size: 1.5rem;
            margin: 10px 0;
        }

        .report-score {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin: 5px 0;
        }

        .report-skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 12px 0;
            padding: 10px 15px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .report-skill-name {
            font-weight: 500;
        }

        .report-skill-score {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
        }

        .report-skill-score.good {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .report-skill-score.needs-improvement {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        @keyframes modalSlideIn {
            from { 
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                width: 95%;
                max-height: 85vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> 学习评价报告生成器</h1>
            <p>智能生成个性化学习评价报告，展示学生的学习成果和进步</p>
        </div>

        <div class="content">
            <div class="controls">
                <button class="btn btn-primary" onclick="generateRandomReport()">
                    <i class="fas fa-magic"></i> 生成随机报告
                </button>
                <button class="btn btn-secondary" onclick="showAllReports()">
                    <i class="fas fa-list"></i> 查看所有报告
                </button>
                <button class="btn btn-secondary" onclick="exportReports()">
                    <i class="fas fa-download"></i> 导出报告
                </button>
                <a href="AI智能评价系统.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回评价系统
                </a>
            </div>

            <div class="reports-grid" id="reportsGrid">
                <!-- 报告卡片将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 详细报告模态框 -->
    <div class="modal" id="reportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">详细学习报告</h2>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详细报告内容将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 优秀学习报告数据 - 体现优秀表现的评价
        const excellentReports = [
            {
                studentName: "学员A",
                reportData: {
                    title: "数据安全学习报告 📊",
                    overallRating: "⭐ ⭐ ⭐ ⭐ ⭐",
                    highlights: [
                        "数据安全意识非常强，能主动识别风险 🛡️",
                        "学习能力出色，掌握知识扎实 📚"
                    ],
                    skillSummary: "已熟练掌握数据安全核心概念，能够灵活运用到实际场景中。",
                    suggestions: [
                        "可以尝试学习更高级的安全防护技术 �",
                        "建议分享经验，帮助其他同学提升 👥"
                    ],
                    conclusion: "表现非常优秀！你已经是班级里的数据安全小专家了，继续保持这种卓越表现！�✨"
                }
            },
            {
                studentName: "学员B",
                reportData: {
                    title: "数据安全学习报告 📊",
                    overallRating: "⭐ ⭐ ⭐ ⭐ ⭐",
                    highlights: [
                        "理论与实践结合能力强，案例分析透彻 🎯",
                        "团队协作表现突出，乐于分享经验 🤝"
                    ],
                    skillSummary: "各项数据安全技能均达到优秀水平，具备独立解决问题的能力。",
                    suggestions: [
                        "可以挑战更复杂的安全场景分析 🧩",
                        "建议担任学习小组长，带领同学共同进步 �"
                    ],
                    conclusion: "小红同学的表现令人印象深刻！你的综合能力已经达到了很高的水准，是其他同学学习的榜样！🌟�️"
                }
            },
            {
                studentName: "小华",
                reportData: {
                    title: "数据安全学习报告 📊",
                    overallRating: "⭐ ⭐ ⭐ ⭐",
                    highlights: [
                        "思维敏捷，能快速理解复杂概念 ⚡",
                        "实践操作能力强，动手能力出色 💪"
                    ],
                    skillSummary: "数据安全技能掌握全面，在密码管理和隐私保护方面表现尤为突出。",
                    suggestions: [
                        "可以深入研究网络安全前沿技术 🔬",
                        "建议参与更多实战演练项目 ⚔️"
                    ],
                    conclusion: "小华同学的技术天赋令人赞叹！你在数据安全领域展现出了巨大的潜力和实力！🚀💎"
                }
            },
            {
                studentName: "小李",
                reportData: {
                    title: "数据安全学习报告 📊",
                    overallRating: "⭐ ⭐ ⭐ ⭐ ⭐",
                    highlights: [
                        "创新思维突出，能提出独特见解 💡",
                        "学习主动性强，自学能力优秀 📖"
                    ],
                    skillSummary: "在风险识别和防护策略制定方面表现卓越，具备专业级水准。",
                    suggestions: [
                        "可以尝试开发自己的安全工具 🛠️",
                        "建议参与网络安全竞赛，展示才华 🏆"
                    ],
                    conclusion: "小李同学是天生的网络安全专家！你的创新能力和专业水平让人刮目相看！🌟🎯"
                }
            },
            {
                studentName: "小张",
                reportData: {
                    title: "数据安全学习报告 📊",
                    overallRating: "⭐ ⭐ ⭐ ⭐",
                    highlights: [
                        "领导能力出众，能有效组织团队学习 👑",
                        "沟通表达能力强，善于知识分享 �️"
                    ],
                    skillSummary: "综合素质优秀，在数据备份和应急响应方面有独到见解。",
                    suggestions: [
                        "可以组织安全知识讲座，传播经验 �",
                        "建议申请成为网络安全志愿者 🦸‍♂️"
                    ],
                    conclusion: "小张同学是优秀的学习领袖！你的组织能力和专业知识让整个团队都受益匪浅！👏�"
                }
            },
            {
                studentName: "小李",
                overallScore: 2,
                mainHighlights: [
                    "思维活跃，善于提问 🤓",
                    "对技术细节很感兴趣 ⚙️"
                ],
                skillLevel: "起步阶段，对技术方面表现出浓厚兴趣。",
                suggestions: [
                    "多了解常见的网络安全威胁 ⚠️",
                    "学习使用基本的安全防护工具 🛠️"
                ],
                conclusion: "小李同学，你的好奇心是最好的老师！继续探索数据安全的奥秘，未来可期！🚀🔬"
            },
            {
                studentName: "小张",
                overallScore: 2,
                mainHighlights: [
                    "团队合作能力强 🤝",
                    "乐于帮助其他同学 💝"
                ],
                skillLevel: "基础阶段，在团队学习中表现积极。",
                suggestions: [
                    "在帮助他人的同时提升自己的安全意识 📈",
                    "学习如何安全地使用社交媒体 📱"
                ],
                conclusion: "小张，你的团队精神很棒！在数据安全的道路上，互帮互助会让大家都进步得更快！🌟👥"
            },
            {
                studentName: "小王",
                overallScore: 1,
                mainHighlights: [
                    "刚开始接触数据安全概念 🌱",
                    "对学习表现出初步兴趣 😊"
                ],
                skillLevel: "入门阶段，需要更多时间来理解基础概念。",
                suggestions: [
                    "从最基本的密码安全开始学习 🔑",
                    "多参与课堂互动，积极提问 🙋‍♂️"
                ],
                conclusion: "小王同学，每个专家都是从新手开始的！相信通过努力学习，你也能掌握数据安全技能！💪🌟"
            },
            {
                studentName: "小刘",
                overallScore: 2,
                mainHighlights: [
                    "学习进步很明显 📈",
                    "对实际案例很感兴趣 📖"
                ],
                skillLevel: "基础阶段，通过案例学习效果显著。",
                suggestions: [
                    "继续通过实际案例加深理解 💡",
                    "尝试在生活中应用学到的安全知识 🏡"
                ],
                conclusion: "小刘，你的进步让老师很欣慰！继续这样学习，你会成为数据安全小能手的！🎉📚"
            },

        ];

        // 简单的报告显示函数 - 按照第一张图片的格式
        function formatSimpleReport(reportData) {
            return `
                <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333;">
                    <h3 style="color: #667eea; margin-bottom: 15px; font-size: 1.1rem;"># ${reportData.title}</h3>

                    <div style="margin-bottom: 15px;">
                        <strong>整体评价：</strong> ${reportData.overallRating}
                    </div>

                    <div style="margin-bottom: 15px;">
                        <strong>主要亮点：</strong>
                        <div style="margin-top: 8px;">
                            ${reportData.highlights.map(highlight => `- ${highlight}`).join('<br>')}
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <strong>技能掌握：</strong> ${reportData.skillSummary}
                    </div>

                    <div style="margin-bottom: 15px;">
                        <strong>改进建议：</strong>
                        <div style="margin-top: 8px;">
                            ${reportData.suggestions.map(suggestion => `- ${suggestion}`).join('<br>')}
                        </div>
                    </div>

                    <div style="margin-top: 20px; padding: 15px; background: rgba(102, 126, 234, 0.05); border-radius: 10px; border-left: 4px solid #667eea;">
                        <strong>结语：</strong><br>
                        ${reportData.conclusion}
                    </div>
                </div>
            `;
        }

        // 随机姓名池
        const randomNames = ["小王", "小刘", "小陈", "小杨", "小赵", "小孙", "小周", "小吴", "小郑", "小冯"];

        // 页面加载时初始化
        window.onload = function() {
            loadExcellentReports();
        };

        // 加载优秀报告
        function loadExcellentReports() {
            showAllReports();
        }

        // 显示所有报告
        function showAllReports() {
            const grid = document.getElementById('reportsGrid');
            grid.innerHTML = '';

            excellentReports.forEach((report, index) => {
                const card = createReportCard(report, index);
                grid.appendChild(card);
            });
        }

        // 创建报告卡片
        function createReportCard(report, index) {
            const card = document.createElement('div');
            card.className = 'report-card';
            card.onclick = () => showDetailedReport(report);

            const reportData = report.reportData;
            const highlights = reportData.highlights.slice(0, 2).map(highlight =>
                `• ${highlight}`
            ).join('<br>');

            card.innerHTML = `
                <div class="report-header">
                    <div class="student-name">学习报告 ${index + 1}</div>
                    <div class="overall-score">${reportData.overallRating}</div>
                </div>
                <div class="highlights">
                    ${highlights}
                    ${reportData.highlights.length > 2 ? '<br>...' : ''}
                </div>
                <div style="margin-top: 10px; font-size: 0.85rem; color: #6c757d;">
                    ${reportData.skillSummary}
                </div>
            `;

            return card;
        }

        // 显示详细报告
        function showDetailedReport(report) {
            const modal = document.getElementById('reportModal');
            const title = document.getElementById('modalTitle');
            const body = document.getElementById('modalBody');

            title.innerHTML = `📊 学习报告`;

            // 使用简单的报告格式
            const htmlReport = formatSimpleReport(report.reportData);

            // 设置报告内容
            body.innerHTML = htmlReport;

            modal.classList.add('show');

            // 添加点击背景关闭功能
            modal.onclick = function(event) {
                if (event.target === modal) {
                    closeModal();
                }
            };

            // 添加ESC键关闭功能
            document.addEventListener('keydown', handleEscKey);
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('reportModal');
            modal.classList.remove('show');
            document.removeEventListener('keydown', handleEscKey);
        }

        // 处理ESC键
        function handleEscKey(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        }

        // 生成随机报告
        function generateRandomReport() {
            const randomName = randomNames[Math.floor(Math.random() * randomNames.length)];

            // 随机选择优秀亮点
            const allHighlights = [
                "数据安全意识非常强，能主动识别风险 🛡️",
                "学习能力出色，掌握知识扎实 📚",
                "理论与实践结合能力强，案例分析透彻 🎯",
                "团队协作表现突出，乐于分享经验 🤝",
                "思维敏捷，能快速理解复杂概念 ⚡",
                "实践操作能力强，动手能力出色 💪",
                "创新思维突出，能提出独特见解 💡",
                "学习主动性强，自学能力优秀 📖",
                "领导能力出众，能有效组织团队学习 �",
                "沟通表达能力强，善于知识分享 �️",
                "问题解决能力突出，逻辑思维清晰 🧩",
                "技术理解深入，能举一反三 🔬"
            ];

            const selectedHighlights = [];
            for (let i = 0; i < 2; i++) {
                const randomHighlight = allHighlights[Math.floor(Math.random() * allHighlights.length)];
                if (!selectedHighlights.includes(randomHighlight)) {
                    selectedHighlights.push(randomHighlight);
                }
            }

            // 随机选择优秀技能水平描述
            const skillLevels = [
                "已熟练掌握数据安全核心概念，能够灵活运用到实际场景中。",
                "各项数据安全技能均达到优秀水平，具备独立解决问题的能力。",
                "数据安全技能掌握全面，在密码管理和隐私保护方面表现尤为突出。",
                "在风险识别和防护策略制定方面表现卓越，具备专业级水准。",
                "综合素质优秀，在数据备份和应急响应方面有独到见解。",
                "技能水平全面均衡，在网络安全防护方面展现出专业素养。"
            ];

            // 随机选择优秀建议
            const allSuggestions = [
                "可以尝试学习更高级的安全防护技术 �",
                "建议分享经验，帮助其他同学提升 👥",
                "可以挑战更复杂的安全场景分析 🧩",
                "建议担任学习小组长，带领同学共同进步 �",
                "可以深入研究网络安全前沿技术 🔬",
                "建议参与更多实战演练项目 ⚔️",
                "可以尝试开发自己的安全工具 🛠️",
                "建议参与网络安全竞赛，展示才华 🏆",
                "可以组织安全知识讲座，传播经验 �",
                "建议申请成为网络安全志愿者 🦸‍♂️"
            ];

            const selectedSuggestions = [];
            for (let i = 0; i < 2; i++) {
                const randomSuggestion = allSuggestions[Math.floor(Math.random() * allSuggestions.length)];
                if (!selectedSuggestions.includes(randomSuggestion)) {
                    selectedSuggestions.push(randomSuggestion);
                }
            }

            // 随机选择优秀结语
            const conclusions = [
                `${randomName}同学表现非常优秀！你已经是班级里的数据安全小专家了，继续保持这种卓越表现！�✨`,
                `${randomName}同学的表现令人印象深刻！你的综合能力已经达到了很高的水准，是其他同学学习的榜样！�🎖️`,
                `${randomName}同学的技术天赋令人赞叹！你在数据安全领域展现出了巨大的潜力和实力！🚀💎`,
                `${randomName}同学是天生的网络安全专家！你的创新能力和专业水平让人刮目相看！🌟🎯`,
                `${randomName}同学是优秀的学习领袖！你的组织能力和专业知识让整个团队都受益匪浅！👏�`
            ];

            // 随机选择4-5星评级
            const ratings = ["⭐ ⭐ ⭐ ⭐", "⭐ ⭐ ⭐ ⭐ ⭐"];

            const newReport = {
                studentName: randomName,
                reportData: {
                    title: "数据安全学习报告 📊",
                    overallRating: ratings[Math.floor(Math.random() * ratings.length)],
                    highlights: selectedHighlights,
                    skillSummary: skillLevels[Math.floor(Math.random() * skillLevels.length)],
                    suggestions: selectedSuggestions,
                    conclusion: conclusions[Math.floor(Math.random() * conclusions.length)]
                }
            };

            // 添加到报告列表
            excellentReports.unshift(newReport);

            // 重新显示所有报告
            showAllReports();

            // 显示生成成功提示
            showNotification(`成功生成${randomName}的学习报告！`);
        }



        // 显示通知
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
                z-index: 2000;
                font-weight: 600;
                animation: slideInRight 0.3s ease-out;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 导出报告
        function exportReports() {
            const reportData = excellentReports.map(report => {
                const data = report.reportData;
                return {
                    学生姓名: report.studentName,
                    星级评价: data.overallRating,
                    主要亮点: data.highlights.join('; '),
                    技能水平: data.skillSummary,
                    改进建议: data.suggestions.join('; '),
                    结语: data.conclusion
                };
            });

            const csvContent = convertToCSV(reportData);
            downloadCSV(csvContent, '学习评价报告.csv');

            showNotification('报告导出成功！');
        }

        // 转换为CSV格式
        function convertToCSV(data) {
            if (data.length === 0) return '';

            const headers = Object.keys(data[0]);
            const csvRows = [headers.join(',')];

            data.forEach(row => {
                const values = headers.map(header => {
                    const value = row[header];
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                });
                csvRows.push(values.join(','));
            });

            return csvRows.join('\n');
        }

        // 下载CSV文件
        function downloadCSV(csvContent, filename) {
            const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
