<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习评价报告生成器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .report-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .student-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #667eea;
        }

        .overall-score {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .stars {
            font-size: 1.5rem;
            margin: 10px 0;
        }

        .skills-preview {
            margin: 15px 0;
        }

        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 8px 12px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .skill-score {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .skill-score.excellent {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }

        .skill-score.good {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .skill-score.needs-improvement {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        .highlights {
            margin-top: 15px;
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .modal-content {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.4s ease-out;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px 20px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 20px 20px 0 0;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #666;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
            color: #333;
        }

        .modal-body {
            padding: 30px;
            line-height: 1.6;
            color: #333;
        }

        /* 原始报告样式 */
        .report-content {
            font-size: 16px;
        }

        .report-content h3 {
            color: #667eea;
            margin: 20px 0 10px 0;
            font-size: 1.2rem;
        }

        .report-content ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .report-content li {
            margin: 8px 0;
        }

        .report-stars {
            font-size: 1.5rem;
            margin: 10px 0;
        }

        .report-score {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin: 5px 0;
        }

        .report-skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 12px 0;
            padding: 10px 15px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .report-skill-name {
            font-weight: 500;
        }

        .report-skill-score {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
        }

        .report-skill-score.good {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .report-skill-score.needs-improvement {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        @keyframes modalSlideIn {
            from { 
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                width: 95%;
                max-height: 85vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> 学习评价报告生成器</h1>
            <p>智能生成个性化学习评价报告，展示学生的学习成果和进步</p>
        </div>

        <div class="content">
            <div class="controls">
                <button class="btn btn-primary" onclick="generateRandomReport()">
                    <i class="fas fa-magic"></i> 生成随机报告
                </button>
                <button class="btn btn-secondary" onclick="showAllReports()">
                    <i class="fas fa-list"></i> 查看所有报告
                </button>
                <button class="btn btn-secondary" onclick="exportReports()">
                    <i class="fas fa-download"></i> 导出报告
                </button>
                <a href="AI智能评价系统.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回评价系统
                </a>
            </div>

            <div class="reports-grid" id="reportsGrid">
                <!-- 报告卡片将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 详细报告模态框 -->
    <div class="modal" id="reportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">详细学习报告</h2>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详细报告内容将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 优秀学习报告数据 - 模拟高质量的学习表现
        const excellentReports = [
            {
                studentName: "小明",
                reportData: generateExcellentReport("小明", {
                    averageLength: 45,
                    totalResponses: 5,
                    securityTerms: 4,
                    hasExamples: 3,
                    hasReasoning: 2,
                    responses: [
                        "我觉得密码安全很重要，因为如果密码被别人知道了，我的账号就不安全了。比如我会设置复杂的密码，不告诉别人。",
                        "分享照片的时候要注意不要暴露位置信息，因为这样坏人可能会知道我在哪里，很危险。",
                        "备份很重要，比如我会把重要的照片和文件保存在云盘里，这样手机坏了也不会丢失。",
                        "我会仔细看链接是不是可疑的，不点击陌生人发的链接，因为可能有病毒。",
                        "在公共WiFi下我不会登录重要账号，因为信息可能被窃取，我会用自己的流量。"
                    ]
                })
            },
            {
                studentName: "小红",
                reportData: generateExcellentReport("小红", {
                    averageLength: 38,
                    totalResponses: 5,
                    securityTerms: 3,
                    hasExamples: 2,
                    hasReasoning: 3,
                    responses: [
                        "我学会了识别钓鱼网站，因为它们经常模仿真正的网站来骗取信息。我会仔细检查网址。",
                        "朋友圈分享要小心，不能发布家庭地址或者行程，因为这些信息可能被坏人利用。",
                        "数据备份让我觉得很安心，我现在会定期备份手机里的重要文件到电脑上。",
                        "公共场所使用WiFi时我会很谨慎，不会在上面做重要的操作，比如网上银行。",
                        "我觉得密码管理很重要，每个账号都用不同的密码，这样更安全。"
                    ]
                })
            },
            {
                studentName: "小华",
                reportData: generateExcellentReport("小华", {
                    averageLength: 42,
                    totalResponses: 5,
                    securityTerms: 4,
                    hasExamples: 3,
                    hasReasoning: 2,
                    responses: [
                        "我觉得数据备份特别重要，因为手机可能会丢失或损坏。我会把照片和重要文件同步到云盘。",
                        "使用公共WiFi时要特别小心，我不会在上面登录重要账号，因为信息可能被窃取。",
                        "我学会了识别钓鱼邮件，它们通常会要求你点击链接或提供密码，这很危险。",
                        "设置密码时我会用复杂的组合，比如包含数字、字母和符号，这样更难被破解。",
                        "在社交媒体上分享时，我会注意隐私设置，不让陌生人看到我的个人信息。"
                    ]
                })
            },
            {
                studentName: "小李",
                reportData: generateExcellentReport("小李", {
                    averageLength: 46,
                    totalResponses: 5,
                    securityTerms: 3,
                    hasExamples: 4,
                    hasReasoning: 3,
                    responses: [
                        "我现在很注意网络安全，比如不随便下载不明软件，因为可能包含病毒或恶意程序。",
                        "朋友圈发照片时我会检查是否包含敏感信息，像家庭住址或学校位置，这些不能暴露。",
                        "我学会了定期更换密码，特别是重要账号，这样即使密码泄露也能及时止损。",
                        "遇到要求提供个人信息的电话或短信，我会先核实对方身份，因为很多是诈骗。",
                        "我会教家人也注意网络安全，比如不要轻信网络中奖信息，这些都是骗局。"
                    ]
                })
            },
            {
                studentName: "小张",
                reportData: generateExcellentReport("小张", {
                    averageLength: 40,
                    totalResponses: 5,
                    securityTerms: 4,
                    hasExamples: 2,
                    hasReasoning: 4,
                    responses: [
                        "我觉得网络安全教育很重要，因为现在网络诈骗手段越来越多，我们必须提高警惕。",
                        "使用网上银行时我会确保网址正确，因为钓鱼网站会模仿真正的银行网站来窃取信息。",
                        "我学会了看邮件发送者地址，因为诈骗邮件通常来自可疑的邮箱地址。",
                        "安装软件时我会选择官方渠道，因为第三方下载站可能会捆绑恶意软件。",
                        "我会定期检查账号的登录记录，这样可以及时发现异常登录并采取措施。"
                    ]
                })
            },
            {
                studentName: "小李",
                overallScore: 2,
                mainHighlights: [
                    "思维活跃，善于提问 🤓",
                    "对技术细节很感兴趣 ⚙️"
                ],
                skillLevel: "起步阶段，对技术方面表现出浓厚兴趣。",
                suggestions: [
                    "多了解常见的网络安全威胁 ⚠️",
                    "学习使用基本的安全防护工具 🛠️"
                ],
                conclusion: "小李同学，你的好奇心是最好的老师！继续探索数据安全的奥秘，未来可期！🚀🔬"
            },
            {
                studentName: "小张",
                overallScore: 2,
                mainHighlights: [
                    "团队合作能力强 🤝",
                    "乐于帮助其他同学 💝"
                ],
                skillLevel: "基础阶段，在团队学习中表现积极。",
                suggestions: [
                    "在帮助他人的同时提升自己的安全意识 📈",
                    "学习如何安全地使用社交媒体 📱"
                ],
                conclusion: "小张，你的团队精神很棒！在数据安全的道路上，互帮互助会让大家都进步得更快！🌟👥"
            },
            {
                studentName: "小王",
                overallScore: 1,
                mainHighlights: [
                    "刚开始接触数据安全概念 🌱",
                    "对学习表现出初步兴趣 😊"
                ],
                skillLevel: "入门阶段，需要更多时间来理解基础概念。",
                suggestions: [
                    "从最基本的密码安全开始学习 🔑",
                    "多参与课堂互动，积极提问 🙋‍♂️"
                ],
                conclusion: "小王同学，每个专家都是从新手开始的！相信通过努力学习，你也能掌握数据安全技能！💪🌟"
            },
            {
                studentName: "小刘",
                overallScore: 2,
                mainHighlights: [
                    "学习进步很明显 📈",
                    "对实际案例很感兴趣 📖"
                ],
                skillLevel: "基础阶段，通过案例学习效果显著。",
                suggestions: [
                    "继续通过实际案例加深理解 💡",
                    "尝试在生活中应用学到的安全知识 🏡"
                ],
                conclusion: "小刘，你的进步让老师很欣慰！继续这样学习，你会成为数据安全小能手的！🎉📚"
            },

        ];

        // 生成优秀报告的核心函数 - 完全复制原始逻辑
        function generateExcellentReport(studentName, userData) {
            const { averageLength, totalResponses, securityTerms, hasExamples, hasReasoning, responses } = userData;

            let report = "📊 **你的数据安全学习报告**\n\n";

            // 计算总体分数 - 确保是优秀分数
            let overallScore = 85; // 优秀基础分数
            if (averageLength > 30) overallScore += 10;
            if (totalResponses >= 4) overallScore += 10;
            if (securityTerms >= 2) overallScore += 10;
            if (hasExamples >= 1) overallScore += 5;
            if (hasReasoning >= 1) overallScore += 5;

            // 星级评价
            const stars = Math.max(4, Math.min(5, Math.ceil(overallScore / 20)));
            report += `🌟 **整体表现**: ${'⭐'.repeat(stars)} (${overallScore}分)\n\n`;

            // 主要亮点
            report += "✨ **学习亮点**:\n";
            report += "• 回答详细认真，思考深入\n";
            report += "• 积极参与对话，学习态度积极\n";
            report += "• 具备良好的安全意识\n";
            report += "• 能够举例说明，理解透彻\n";
            report += "• 逻辑思维清晰，有条理\n";

            // 技能掌握（基于回答内容分析）
            report += "\n🎯 **技能掌握情况**:\n";

            let hazardScore = 85;
            let sharingScore = 85;
            let backupScore = 85;
            let protectionScore = 85;

            // 根据回答内容调整分数
            responses.forEach(response => {
                const content = response.toLowerCase();
                if (/隐患|危险|风险|陌生人|链接|二维码|钓鱼/.test(content)) hazardScore += 10;
                if (/分享|朋友圈|隐私|位置|照片/.test(content)) sharingScore += 10;
                if (/备份|保存|存储|丢失|重要/.test(content)) backupScore += 10;
                if (/保护|措施|密码|wifi|安全/.test(content)) protectionScore += 10;
            });

            hazardScore = Math.min(hazardScore, 95);
            sharingScore = Math.min(sharingScore, 95);
            backupScore = Math.min(backupScore, 95);
            protectionScore = Math.min(protectionScore, 95);

            report += `• 隐患识别: ${hazardScore}分 (${hazardScore >= 80 ? "优秀" : "良好"})\n`;
            report += `• 安全分享: ${sharingScore}分 (${sharingScore >= 80 ? "优秀" : "良好"})\n`;
            report += `• 数据备份: ${backupScore}分 (${backupScore >= 80 ? "优秀" : "良好"})\n`;
            report += `• 保护措施: ${protectionScore}分 (${protectionScore >= 80 ? "优秀" : "良好"})\n`;

            // 改进建议
            report += "\n💡 **改进建议**:\n";
            report += "• 继续保持这种优秀的学习态度\n";
            report += "• 可以尝试学习更高级的安全知识\n";
            report += "• 建议向同学分享你的安全经验\n";

            // 鼓励结语
            report += "\n🎉 太棒了！你已经是数据安全小专家了！继续保持这种学习热情！";

            return report;
        }

        // 将文本报告转换为HTML格式 - 完全复制原始函数
        function convertReportToHTML(report) {
            // 将Markdown风格的文本转换为HTML
            let html = report
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // **粗体**
                .replace(/\n\n/g, '</p><p>') // 段落分隔
                .replace(/\n/g, '<br>') // 换行
                .replace(/• /g, '<li>') // 列表项
                .replace(/<br><li>/g, '</p><ul><li>') // 列表开始
                .replace(/<li>(.*?)(<br>|<\/p>)/g, '<li>$1</li>') // 列表项结束
                .replace(/(<\/li>)(<br>)*(<strong>)/g, '</ul><p><strong>'); // 列表结束

            // 包装在段落中
            html = '<p>' + html + '</p>';

            // 处理星级显示
            html = html.replace(/(⭐+)/g, '<span class="report-stars">$1</span>');

            // 处理分数显示
            html = html.replace(/\((\d+分)\)/g, '<span class="report-score">$1</span>');

            // 处理技能分数项
            html = html.replace(/• (.*?): (\d+分) \((.*?)\)/g, function(match, skill, score, level) {
                let scoreClass = 'report-skill-score';
                if (level === '良好') scoreClass += ' good';
                if (level === '需加强') scoreClass += ' needs-improvement';

                return `<div class="report-skill-item">
                    <span class="report-skill-name">${skill}</span>
                    <span class="${scoreClass}">${score} (${level})</span>
                </div>`;
            });

            // 清理多余的标签
            html = html
                .replace(/<p><\/p>/g, '')
                .replace(/<p><br>/g, '<p>')
                .replace(/<br><\/p>/g, '</p>')
                .replace(/(<ul><li>.*?<\/li>)<br>/g, '$1')
                .replace(/<\/ul><br>/g, '</ul>');

            return html;
        }

        // 随机姓名池
        const randomNames = ["小王", "小刘", "小陈", "小杨", "小赵", "小孙", "小周", "小吴", "小郑", "小冯"];

        // 页面加载时初始化
        window.onload = function() {
            loadExcellentReports();
        };

        // 加载优秀报告
        function loadExcellentReports() {
            showAllReports();
        }

        // 显示所有报告
        function showAllReports() {
            const grid = document.getElementById('reportsGrid');
            grid.innerHTML = '';

            excellentReports.forEach((report, index) => {
                const card = createReportCard(report, index);
                grid.appendChild(card);
            });
        }

        // 创建报告卡片
        function createReportCard(report, index) {
            const card = document.createElement('div');
            card.className = 'report-card';
            card.onclick = () => showDetailedReport(report);

            // 从报告数据中提取星级和分数
            const reportText = report.reportData;
            const scoreMatch = reportText.match(/(\d+)分/);
            const starsMatch = reportText.match(/(⭐+)/);
            const score = scoreMatch ? scoreMatch[1] : '90';
            const stars = starsMatch ? starsMatch[1] : '⭐⭐⭐⭐⭐';

            // 提取主要亮点
            const highlightsMatch = reportText.match(/学习亮点\*\*:(.*?)🎯/s);
            let highlights = "• 学习态度积极，表现优秀<br>• 安全意识强，理解深入";
            if (highlightsMatch) {
                const highlightsList = highlightsMatch[1].match(/• ([^\n]+)/g);
                if (highlightsList && highlightsList.length >= 2) {
                    highlights = highlightsList.slice(0, 2).join('<br>');
                }
            }

            card.innerHTML = `
                <div class="report-header">
                    <div class="student-name">${report.studentName}</div>
                    <div class="overall-score">${score}分</div>
                </div>
                <div class="stars">${stars}</div>
                <div class="highlights">
                    ${highlights}
                </div>
                <div style="margin-top: 10px; font-size: 0.85rem; color: #6c757d;">
                    数据安全学习表现优秀
                </div>
            `;

            return card;
        }

        // 显示详细报告
        function showDetailedReport(report) {
            const modal = document.getElementById('reportModal');
            const title = document.getElementById('modalTitle');
            const body = document.getElementById('modalBody');

            title.innerHTML = `📊 你的学习报告`;

            // 将报告内容转换为HTML格式
            const htmlReport = convertReportToHTML(report.reportData);

            // 设置报告内容
            body.innerHTML = `<div class="report-content">${htmlReport}</div>`;

            modal.classList.add('show');

            // 添加点击背景关闭功能
            modal.onclick = function(event) {
                if (event.target === modal) {
                    closeModal();
                }
            };

            // 添加ESC键关闭功能
            document.addEventListener('keydown', handleEscKey);
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('reportModal');
            modal.classList.remove('show');
            document.removeEventListener('keydown', handleEscKey);
        }

        // 处理ESC键
        function handleEscKey(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        }

        // 生成随机报告
        function generateRandomReport() {
            const randomName = randomNames[Math.floor(Math.random() * randomNames.length)];

            // 生成优秀的学习数据
            const excellentResponses = [
                "我学会了设置强密码，包含大小写字母、数字和符号，这样更安全。而且每个账号都用不同的密码。",
                "分享照片时我会注意隐私设置，不会暴露具体位置信息，因为这可能被坏人利用来跟踪我。",
                "我现在会定期备份重要文件，比如学习资料和照片，保存在云盘和移动硬盘里，双重保险。",
                "遇到可疑链接我会仔细检查，看域名是否正确，不轻易点击，因为可能是钓鱼网站。",
                "在公共WiFi环境下，我不会登录银行账户或输入重要密码，会使用自己的手机流量。",
                "我学会了识别诈骗电话和短信，不会轻易透露个人信息，会先核实对方身份。"
            ];

            // 随机选择5个优秀回答
            const selectedResponses = [];
            for (let i = 0; i < 5; i++) {
                const randomResponse = excellentResponses[Math.floor(Math.random() * excellentResponses.length)];
                if (!selectedResponses.includes(randomResponse)) {
                    selectedResponses.push(randomResponse);
                }
            }

            // 生成优秀的学习数据
            const userData = {
                averageLength: 40 + Math.floor(Math.random() * 20), // 40-60字符
                totalResponses: 5,
                securityTerms: 3 + Math.floor(Math.random() * 2), // 3-4个安全术语
                hasExamples: 2 + Math.floor(Math.random() * 2), // 2-3个例子
                hasReasoning: 2 + Math.floor(Math.random() * 2), // 2-3个推理
                responses: selectedResponses
            };

            const newReport = {
                studentName: randomName,
                reportData: generateExcellentReport(randomName, userData)
            };

            // 添加到报告列表
            excellentReports.unshift(newReport);

            // 重新显示所有报告
            showAllReports();

            // 显示生成成功提示
            showNotification(`成功生成${randomName}的优秀学习报告！`);
        }



        // 显示通知
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
                z-index: 2000;
                font-weight: 600;
                animation: slideInRight 0.3s ease-out;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 导出报告
        function exportReports() {
            const reportData = excellentReports.map(report => {
                // 从报告数据中提取信息
                const reportText = report.reportData;
                const scoreMatch = reportText.match(/(\d+)分/);
                const starsMatch = reportText.match(/(⭐+)/);

                return {
                    学生姓名: report.studentName,
                    总体分数: scoreMatch ? scoreMatch[1] + '分' : '90分',
                    星级评价: starsMatch ? starsMatch[1] : '⭐⭐⭐⭐⭐',
                    报告内容: reportText.replace(/\*\*/g, '').replace(/\n/g, ' ')
                };
            });

            const csvContent = convertToCSV(reportData);
            downloadCSV(csvContent, '优秀学习评价报告.csv');

            showNotification('优秀报告导出成功！');
        }

        // 转换为CSV格式
        function convertToCSV(data) {
            if (data.length === 0) return '';

            const headers = Object.keys(data[0]);
            const csvRows = [headers.join(',')];

            data.forEach(row => {
                const values = headers.map(header => {
                    const value = row[header];
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                });
                csvRows.push(values.join(','));
            });

            return csvRows.join('\n');
        }

        // 下载CSV文件
        function downloadCSV(csvContent, filename) {
            const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
